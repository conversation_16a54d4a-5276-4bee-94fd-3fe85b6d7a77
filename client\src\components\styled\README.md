# Styled Components Library

This directory contains the styled components library for the Pigeon Squad application, built on top of Material-UI v7 with Emotion styling engine.

## 📁 Structure

```
styled/
├── index.ts                    # Main exports
├── DashboardComponents.ts      # Dashboard-specific styled components
├── examples.tsx               # Usage examples and demos
├── README.md                  # This documentation
└── ...                        # Additional component files
```

## 🎯 Benefits

### Performance
- **Zero runtime CSS-in-JS overhead** - Styles are extracted at build time
- **Smaller bundle size** - No runtime style generation
- **Better caching** - Static CSS can be cached effectively

### Developer Experience
- **Type safety** - Full TypeScript support with prop validation
- **Reusability** - Create component variants and design system
- **Cleaner code** - Separate styling concerns from component logic
- **Better debugging** - Clear component names in dev tools

### Design System
- **Consistent spacing** - Unified spacing system across components
- **Theme integration** - Direct access to theme tokens
- **Component variants** - Predefined styles for different use cases
- **Responsive design** - Built-in responsive utilities

## 🚀 Quick Start

### Basic Usage

```tsx
import { Styled<PERSON>ontainer, Flex<PERSON>ontainer, StyledCard } from '@/components/styled';

function MyComponent() {
  return (
    <StyledContainer spacing={3}>
      <FlexContainer justify="space-between" align="center" gap={2}>
        <Typography variant="h5">Title</Typography>
        <Button variant="contained">Action</Button>
      </FlexContainer>
      
      <StyledCard interactive priority="high">
        <CardContent>
          <Typography>Interactive card with priority styling</Typography>
        </CardContent>
      </StyledCard>
    </StyledContainer>
  );
}
```

### Dashboard Components

```tsx
import { 
  StatsGrid, 
  StatCard, 
  StatCardContent,
  ActivityCard,
  PriorityChip 
} from '@/components/styled/DashboardComponents';

function Dashboard() {
  return (
    <StatsGrid>
      <StatCard>
        <StatCardContent>
          <StatIcon>📧</StatIcon>
          <StatNumber>12</StatNumber>
          <StatLabel>New Messages</StatLabel>
        </StatCardContent>
      </StatCard>
    </StatsGrid>
  );
}
```

## 📚 Component Categories

### Layout Components
- `StyledContainer` - Flexible container with consistent padding
- `FlexContainer` - Flex layout with customizable properties
- `GridContainer` - CSS Grid with responsive columns
- `Section` - Page section wrapper
- `PageHeader` - Header with title and actions

### Card Components
- `StyledCard` - Enhanced card with priority and interaction states
- `StatCard` - Specialized card for dashboard statistics
- `ActivityCard` - Card for activity items with priority borders

### Typography Components
- `StyledTypography` - Enhanced typography with color variants
- `IconTypography` - Consistent emoji/icon sizing
- `StatNumber` - Large numbers for statistics
- `StatLabel` - Labels for statistics

### Dashboard Specific
- `DashboardContainer` - Main dashboard layout
- `TabContent` - Tab content with consistent padding
- `StatsGrid` - Grid layout for statistics
- `PriorityChip` - Chips with priority colors
- `ActivityHeader` - Header for activity items

## 🎨 Theme Integration

### Priority Colors
```tsx
<PriorityChip priority="high" label="Urgent" />
<ActivityCard priority="medium">...</ActivityCard>
```

### Custom Theme Properties
```tsx
// Access custom theme properties
const MyComponent = styled(Box)(({ theme }) => ({
  boxShadow: theme.custom.shadows.cardElevated,
  color: theme.custom.priority.high.main,
}));
```

### Responsive Values
```tsx
<GridContainer 
  columns={{ xs: 1, sm: 2, md: 3, lg: 4 }}
  gap={3}
  minWidth="250px"
/>
```

## 🔧 Utility Functions

### Hover Effects
```tsx
import { createHoverEffect } from '@/components/styled';

const HoverCard = styled(Card)(({ theme }) => ({
  ...createHoverEffect(theme, 'medium'),
}));
```

### Text Truncation
```tsx
import { createTruncatedText } from '@/components/styled';

const TruncatedText = styled(Typography)(() => ({
  ...createTruncatedText(2), // Clamp to 2 lines
}));
```

### Responsive Spacing
```tsx
import { createResponsiveSpacing } from '@/components/styled';

const ResponsiveBox = styled(Box)(({ theme }) => ({
  ...createResponsiveSpacing(theme, 2, 3, 4, 5), // xs, sm, md, lg
}));
```

## 🎯 Migration Guide

### From `sx` prop to Styled Components

**Before:**
```tsx
<Box sx={{ 
  p: 3, 
  display: 'flex', 
  justifyContent: 'space-between',
  alignItems: 'center' 
}}>
```

**After:**
```tsx
<FlexContainer justify="space-between" align="center" spacing={3}>
```

### From inline styles to Styled Components

**Before:**
```tsx
<Card style={{ 
  borderLeft: '4px solid red',
  cursor: 'pointer',
  transition: 'transform 0.2s'
}}>
```

**After:**
```tsx
<StyledCard priority="high" interactive>
```

## 🧪 Testing

Components include proper `shouldForwardProp` configuration to prevent style props from being passed to DOM elements:

```tsx
const StyledCard = styled(Card, {
  shouldForwardProp: (prop) => !['priority', 'interactive'].includes(prop as string),
})<{ priority?: string; interactive?: boolean }>(/* styles */);
```

## 📖 Examples

See `examples.tsx` for comprehensive usage examples including:
- Stats grids with hover effects
- Activity cards with priority styling
- Flexible layout components
- Before/after comparisons
- Theme integration examples

## 🔄 Future Enhancements

- [ ] Animation utilities
- [ ] Form-specific styled components
- [ ] Data visualization components
- [ ] Mobile-specific variants
- [ ] Dark mode optimizations
- [ ] Accessibility enhancements

## 💡 Best Practices

1. **Use semantic component names** - `StatCard` instead of `BlueCard`
2. **Leverage theme tokens** - Use `theme.spacing()` instead of hardcoded values
3. **Include hover states** - Enhance interactivity with subtle animations
4. **Consider responsive design** - Use responsive utilities for mobile-first design
5. **Document prop interfaces** - Include TypeScript interfaces for custom props
6. **Test accessibility** - Ensure proper focus states and ARIA attributes
