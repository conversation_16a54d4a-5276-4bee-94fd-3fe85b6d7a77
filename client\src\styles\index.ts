// =============================================================================
// MAIN STYLES EXPORTS - NEW MODULAR STRUCTURE
// =============================================================================

// Styled components
export * from './components';

// Utility functions
export * from './utils';

// Type definitions
export * from './types';

// Theme extensions (existing)
export * from './themeExtensions';

// =============================================================================
// BACKWARD COMPATIBILITY EXPORTS
// =============================================================================
// These re-exports maintain compatibility with existing imports
// while encouraging migration to the new structure

// Layout components
export {
  StyledContainer,
  FlexContainer,
  GridContainer,
  Section,
  PageHeader,
  EmptyState,
} from './components/layout';

// Card components
export {
  StyledCard,
  StatCard,
  StatCardContent,
} from './components/cards';

// Typography components
export {
  StyledTypography,
  IconTypography,
} from './components/typography';

// Utility functions
export {
  getPriorityColor,
  createResponsiveSpacing,
  createHoverEffect,
  createFocusRing,
} from './utils';
