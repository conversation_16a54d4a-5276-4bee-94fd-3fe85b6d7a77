import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  SwipeableDrawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Collapse,
  Chip,
  Avatar,
  IconButton,
  Button,
  useTheme,
  useMediaQuery,
  Tooltip,
} from '@mui/material';
import {
  ExpandLess,
  ExpandMore,
  FiberManualRecord,
  ChevronLeft,
  Logout,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { User } from '../../../types';
import { navigationConfig, NavigationItem } from '../../../config/navigation';
import LogoSection from '../../Public/NavBar/LogoSection';

const DRAWER_WIDTH = 280;
const MINI_DRAWER_WIDTH = 64;

interface NavigationDrawerProps {
  open: boolean;
  onClose: () => void;
  onOpen?: () => void;
  user: User | null;
  onLogout: () => void;
  variant?: 'temporary' | 'permanent' | 'persistent';
  mini?: boolean;
  onToggleMini?: () => void;
}

// Styled Components
const StyledDrawer = styled(Drawer, {
  shouldForwardProp: (prop) => prop !== 'mini',
})<{ mini?: boolean }>(({ theme, mini }) => ({
  width: mini ? MINI_DRAWER_WIDTH : DRAWER_WIDTH,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  '& .MuiDrawer-paper': {
    width: mini ? MINI_DRAWER_WIDTH : DRAWER_WIDTH,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.mode === 'dark' ? '#1a1a1a' : '#ffffff',
    borderRight: `1px solid ${theme.palette.divider}`,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: 'hidden',
    [theme.breakpoints.down('md')]: {
      width: '100vw',
      maxWidth: DRAWER_WIDTH,
    },
  },
}));

const DrawerHeader = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'mini',
})<{ mini?: boolean }>(({ theme, mini }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: mini ? 'center' : 'space-between',
  padding: theme.spacing(2, 3),
  minHeight: 64,
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const LogoContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'mini',
})<{ mini?: boolean }>(({ theme, mini }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 12,
  opacity: mini ? 0 : 1,
  transition: theme.transitions.create('opacity', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.short,
  }),
}));



const CloseButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));


const StyledListItemButton = styled(ListItemButton, {
  shouldForwardProp: (prop) => !['active', 'isNew', 'mini', 'level'].includes(prop as string),
})<{ active?: boolean; isNew?: boolean; mini?: boolean; level?: number }>(({ theme, active, isNew, mini, level = 0 }) => ({
  margin: theme.spacing(0.5, 1.5),
  borderRadius: theme.spacing(1),
  minHeight: 44,
  paddingLeft: theme.spacing(3 + level * 2),
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' 
      ? 'rgba(255, 255, 255, 0.08)' 
      : 'rgba(0, 0, 0, 0.04)',
  },
  ...(active && {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.dark,
    },
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.contrastText,
    },
    '& .MuiListItemText-primary': {
      fontWeight: 600,
    },
  }),
  ...(isNew && {
    position: 'relative',
    '&::after': {
      content: '"New"',
      position: 'absolute',
      right: 8,
      top: '50%',
      transform: 'translateY(-50%)',
      backgroundColor: theme.palette.success.main,
      color: theme.palette.success.contrastText,
      fontSize: '0.625rem',
      fontWeight: 600,
      padding: '2px 6px',
      borderRadius: 4,
      textTransform: 'uppercase',
      display: mini ? 'none' : 'block',
    },
  }),
  ...(mini && {
    margin: theme.spacing(0.5, 1),
    justifyContent: 'center',
    '& .MuiListItemText-root': {
      display: 'none',
    },
  }),
}));

const UserSection = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'mini',
})<{ mini?: boolean }>(({ theme, mini }) => ({
  marginTop: 'auto',
  padding: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.divider}`,
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
  alignItems: mini ? 'center' : 'stretch',
}));

const UserProfile = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1),
  backgroundColor: theme.palette.mode === 'dark'
    ? 'rgba(255, 255, 255, 0.05)'
    : 'rgba(0, 0, 0, 0.02)',
}));

// Additional styled components for remaining sx props
const BlockListItem = styled(ListItem)(() => ({
  display: 'block',
}));

const StyledListItemIcon = styled(ListItemIcon)(() => ({
  minWidth: 40,
}));

const SmallIcon = styled(FiberManualRecord)(() => ({
  fontSize: 8,
}));

const FlexColumnBox = styled(Box)(() => ({
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
}));

const FlexAutoBox = styled(Box)(() => ({
  flex: 1,
  overflow: 'auto',
}));

const FlexMinWidthBox = styled(Box)(() => ({
  flex: 1,
  minWidth: 0,
}));

const StyledAvatar = styled(Avatar)(() => ({
  width: 40,
  height: 40,
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  height: 20,
  fontSize: '0.625rem',
  '& .MuiChip-label': {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
  },
}));

const LogoutButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(1),
}));

const StyledSwipeableDrawer = styled(SwipeableDrawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    boxSizing: 'border-box',
    backgroundColor: theme.palette.mode === 'dark' ? '#1a1a1a' : '#ffffff',
    borderRight: `1px solid ${theme.palette.divider}`,
  },
}));

const NavigationDrawer: React.FC<NavigationDrawerProps> = ({
  open,
  onClose,
  onOpen,
  user,
  onLogout,
  variant = 'temporary',
  mini = false,
  onToggleMini,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleItemClick = (item: NavigationItem) => {
    if (item.children) {
      // Toggle expansion for items with children
      setExpandedItems(prev => 
        prev.includes(item.id) 
          ? prev.filter(id => id !== item.id)
          : [...prev, item.id]
      );
    } else if (item.path) {
      // Navigate to the path
      if (item.path.startsWith('http')) {
        window.open(item.path, '_blank');
      } else if (item.id === 'logout') {
        onLogout();
      } else {
        navigate(item.path);
        if (isMobile) {
          onClose();
        }
      }
    }
  };

  const isItemActive = (item: NavigationItem): boolean => {
    return location.pathname === item.path;
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isActive = isItemActive(item);
    const isExpanded = expandedItems.includes(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.id}>
        <BlockListItem disablePadding>
          {mini && !hasChildren ? (
            <Tooltip title={item.label} placement="right">
              <StyledListItemButton
                active={isActive}
                isNew={item.isNew}
                mini={mini}
                level={level}
                onClick={() => handleItemClick(item)}
              >
                <StyledListItemIcon>
                  {level > 0 ? (
                    <SmallIcon />
                  ) : (
                    <item.icon />
                  )}
                </StyledListItemIcon>
                <ListItemText
                  primary={item.label}
                  slotProps={{
                    primary: {
                      fontSize: '0.875rem',
                      fontWeight: isActive ? 600 : 400,
                    }
                  }}
                />
              </StyledListItemButton>
            </Tooltip>
          ) : (
            <StyledListItemButton
              active={isActive}
              isNew={item.isNew}
              mini={mini}
              level={level}
              onClick={() => handleItemClick(item)}
            >
              <StyledListItemIcon>
                {level > 0 ? (
                  <SmallIcon />
                ) : (
                  <item.icon />
                )}
              </StyledListItemIcon>
              <ListItemText
                primary={item.label}
                slotProps={{
                  primary: {
                    fontSize: '0.875rem',
                    fontWeight: isActive ? 600 : 400,
                  }
                }}
              />
              {item.badge && (
                <StyledChip
                  label={item.badge}
                  size="small"
                  color={typeof item.badge === 'string' ? 'default' : 'primary'}
                />
              )}
              {hasChildren && (
                isExpanded ? <ExpandLess /> : <ExpandMore />
              )}
            </StyledListItemButton>
          )}
        </BlockListItem>
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderNavigationItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawerContent = (
    <FlexColumnBox>
      {/* Header */}
      <DrawerHeader mini={mini}>
        <LogoContainer mini={mini}>
          <LogoSection />
        </LogoContainer>
        {!isMobile && onToggleMini && (
          <CloseButton onClick={onToggleMini}>
            <ChevronLeft />
          </CloseButton>
        )}
      </DrawerHeader>

      {/* Navigation Items */}
      <FlexAutoBox>
        <List>
          {navigationConfig.map(item => renderNavigationItem(item))}
        </List>
      </FlexAutoBox>

      {/* User Section */}
      {user && (
        <UserSection mini={mini}>
          {mini ? (
            <>
              <Tooltip title={user.name || 'User'} placement="right">
                <StyledAvatar>
                  {user.name?.charAt(0) || user.email.charAt(0)}
                </StyledAvatar>
              </Tooltip>
              <Tooltip title="Logout" placement="right">
                <IconButton onClick={onLogout} size="small">
                  <Logout />
                </IconButton>
              </Tooltip>
            </>
          ) : (
            <>
              <UserProfile>
                <StyledAvatar>
                  {user.name?.charAt(0) || user.email.charAt(0)}
                </StyledAvatar>
                <FlexMinWidthBox>
                  <Typography variant="subtitle2" fontWeight={600} noWrap>
                    {user.name || 'User'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" noWrap>
                    Designer
                  </Typography>
                </FlexMinWidthBox>
              </UserProfile>
              <LogoutButton
                variant="outlined"
                startIcon={<Logout />}
                onClick={onLogout}
                size="small"
                fullWidth
              >
                Logout
              </LogoutButton>
            </>
          )}
        </UserSection>
      )}
    </FlexColumnBox>
  );

  // Use SwipeableDrawer for mobile, regular Drawer for desktop
  if (isMobile && variant === 'temporary') {
    return (
      <StyledSwipeableDrawer
        anchor="left"
        open={open}
        onClose={onClose}
        onOpen={onOpen || (() => {})}
        disableBackdropTransition={false}
        disableDiscovery={false}
      >
        {drawerContent}
      </StyledSwipeableDrawer>
    );
  }

  return (
    <StyledDrawer
      variant={variant}
      anchor="left"
      open={open}
      onClose={onClose}
      mini={mini}
      ModalProps={{
        keepMounted: true, // Better open performance on mobile
      }}
    >
      {drawerContent}
    </StyledDrawer>
  );
};

export default NavigationDrawer;
