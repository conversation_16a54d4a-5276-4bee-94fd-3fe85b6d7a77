import Imap from 'imap';
import { simpleParser, ParsedMail } from 'mailparser';
import GeminiService from './geminiService';

class ImapMonitorService {
  private imap: any;
  private isConnected: boolean = false;
  private geminiService: GeminiService;
  private monitorInterval: NodeJS.Timeout | null = null;
  private emailFilter: string;

  constructor() {
    this.imap = new Imap({
      user: process.env.GMAIL_EMAIL!,
      password: process.env.GMAIL_APP_PASSWORD!,
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    });

    this.geminiService = new GeminiService();
    this.emailFilter = process.env.EMAIL_FILTER || '+dev';
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.imap.once('ready', () => {
      console.log('\x1b[32m📧 IMAP connection ready\x1b[0m');
      this.isConnected = true;
      this.openInbox();
    });

    this.imap.once('error', (err: Error) => {
      console.error('\x1b[31m❌ IMAP connection error:\x1b[0m', err.message);
    });

    this.imap.once('end', () => {
      console.log('\x1b[33m📪 IMAP connection ended\x1b[0m');
      this.isConnected = false;
    });
  }

  start(): void {
    console.log('\x1b[36m🔍 Starting IMAP email monitor...\x1b[0m');
    this.imap.connect();
  }

  stop(): void {
    console.log('\x1b[33m⏹️  Stopping IMAP monitor...\x1b[0m');
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    if (this.isConnected) {
      this.imap.end();
    }
    // Force close after timeout
    setTimeout(() => {
      if (this.isConnected) {
        console.log('\x1b[31m🔌 Force closing IMAP connection...\x1b[0m');
        this.imap.destroy();
      }
    }, 2000);
  }

  private openInbox(): void {
    this.imap.openBox('INBOX', false, (err: any, box: any) => {
      if (err) {
        console.error('Error opening inbox:', err);
        return;
      }

      console.log(`\x1b[36m📬 Monitoring Inbox with ${box.messages.total} (total) messages\x1b[0m`);
      this.analyzeInboxMessages(box.messages.total);
      this.watchForNewMessages();
    });
  }

  private watchForNewMessages(): void {
    this.imap.on('mail', (numNewMsgs: number) => {
      console.log(`\x1b[32m✉️  ${numNewMsgs} new message(s) received\x1b[0m`);
      this.fetchNewMessages();
    });

    // Also periodically check for manually unread messages
    this.monitorInterval = setInterval(() => {
      this.checkForUnreadMessages();
    }, 30000); // Check every 30 seconds
  }

  private analyzeInboxMessages(totalMessages: number): void {
    // Search for all messages to count +share messages
    this.imap.search(['ALL'], (err: any, allResults: any) => {
      if (err) {
        console.error('Error searching all messages:', err);
        return;
      }

      // Search for unread messages
      this.imap.search(['UNSEEN'], (err: any, unreadResults: any) => {
        if (err) {
          console.error('Error searching unread messages:', err);
          return;
        }

        // Count +share messages and processed messages
        this.countShareMessages(allResults, unreadResults, totalMessages);
      });
    });
  }

  private countShareMessages(allUids: number[], unreadUids: number[], totalMessages: number): void {
    if (allUids.length === 0) {
      console.log(`\x1b[33m📊 Found 0 messages with ${this.emailFilter}\x1b[0m`);
      console.log(`\x1b[33m📊 Found 0 messages already processed\x1b[0m`);
      console.log(`\x1b[33m📊 Found 0 messages unread, ready to process\x1b[0m`);
      return;
    }

    const fetch = this.imap.fetch(allUids.slice(0, 50), { // Limit to recent 50 for performance
      bodies: 'HEADER.FIELDS (TO)',
      markSeen: false
    });

    let shareCount = 0;
    let processedCount = 0;

    fetch.on('message', (msg: any) => {
      msg.on('body', (stream: any) => {
        let buffer = '';
        stream.on('data', (chunk: any) => {
          buffer += chunk.toString();
        });
        stream.once('end', () => {
          if (buffer.includes(this.emailFilter)) {
            shareCount++;
          }
        });
      });
    });

    fetch.once('end', () => {
      processedCount = Math.max(0, shareCount - unreadUids.length);
      const unreadShareCount = Math.min(shareCount, unreadUids.length);
      
      console.log(`\x1b[33m📊 Found ${shareCount} messages with ${this.emailFilter}\x1b[0m`);
      console.log(`\x1b[33m📊 Found ${processedCount} messages already processed\x1b[0m`);
      console.log(`\x1b[33m📊 Found ${unreadShareCount} messages unread, ready to process\x1b[0m`);
      
      // Now process unread messages
      if (unreadUids.length > 0) {
        this.fetchMessages(unreadUids);
      }
    });

    fetch.once('error', (err: any) => {
      console.error('Error counting share messages:', err);
      // Fallback to simple unread processing
      if (unreadUids.length > 0) {
        this.fetchMessages(unreadUids);
      }
    });
  }

  private checkForUnreadMessages(): void {
    this.imap.search(['UNSEEN'], (err: any, results: any) => {
      if (err) {
        console.error('Error checking for unread messages:', err);
        return;
      }

      if (results.length > 0) {
        console.log(`\x1b[35m🔄 Found ${results.length} unread messages during periodic check\x1b[0m`);
        this.fetchMessages(results);
      }
    });
  }

  private fetchNewMessages(): void {
    const fetch = this.imap.seq.fetch('*:*', {
      bodies: '',
      markSeen: false
    });

    fetch.on('message', (msg: any, seqno: any) => {
      this.processMessage(msg, seqno, false);
    });

    fetch.once('error', (err: any) => {
      console.error('Fetch error:', err);
    });
  }

  private fetchMessages(uids: number[]): void {
    const fetch = this.imap.fetch(uids, {
      bodies: '',
      markSeen: false // Don't mark as seen initially
    });

    fetch.on('message', (msg: any, seqno: any) => {
      this.processMessage(msg, seqno, true); // Pass flag for filtering
    });

    fetch.once('error', (err: any) => {
      console.error('Fetch error:', err);
    });
  }

  private processMessage(msg: any, seqno: any, filterForShare: boolean = false): void {
    let messageUid: number;

    msg.once('attributes', (attrs: any) => {
      messageUid = attrs.uid;
      // Mark as read immediately after downloading
      this.imap.addFlags(messageUid, '\\Seen', (err: any) => {
        if (err) console.error('Error marking as read:', err);
      });
    });

    msg.on('body', (stream: any) => {
      simpleParser(stream, (err: any, parsed: ParsedMail) => {
        if (err) {
          console.error('Error parsing message:', err);
          return;
        }

        const toAddresses = Array.isArray(parsed.to) ? parsed.to.map(t => t.text).join(', ') : parsed.to?.text || '';
        const hasFilter = toAddresses.includes(this.emailFilter);

        // If filtering for email filter and this email doesn't have it, skip processing
        if (filterForShare && !hasFilter) {
          return;
        }

        console.log('\x1b[44m\x1b[37m📨 === EMAIL PROCESSED ===\x1b[0m');
        console.log(`\x1b[36mFrom:\x1b[0m ${parsed.from?.text || 'Unknown'}`);
        console.log(`\x1b[36mTo:\x1b[0m ${toAddresses}`);
        console.log(`\x1b[36mSubject:\x1b[0m ${parsed.subject || 'No subject'}`);
        console.log(`\x1b[36mBody:\x1b[0m ${(parsed.text || '').substring(0, 200)}...`);
        console.log(`\x1b[36mDate:\x1b[0m ${parsed.date}`);
        console.log('\x1b[44m\x1b[37m========================\x1b[0m');

        // Only analyze emails sent to filtered address (async/parallel)
        if (hasFilter) {
          this.processWithGemini(parsed);
        } else {
          console.log(`\x1b[33m⏭️  Skipping AI analysis - not sent to ${this.emailFilter} address\x1b[0m`);
        }
      });
    });
  }

  private async processWithGemini(parsed: ParsedMail): Promise<void> {
    try {
      console.log('\x1b[35m🤖 Starting Gemini analysis (async)...\x1b[0m');
      await this.geminiService.analyzeEmail(parsed);
    } catch (error) {
      console.error('\x1b[31m❌ Gemini analysis failed:\x1b[0m', error);
    }
  }
}

export default ImapMonitorService;