import { styled } from '@mui/material/styles';
import { Card, CardContent } from '@mui/material';
import { PriorityProps } from '../../types/props';

// =============================================================================
// CARD COMPONENTS
// =============================================================================

/**
 * Enhanced card with priority styling and hover effects
 */
export const StyledCard = styled(Card, {
  shouldForwardProp: (prop) => !['priority', 'interactive', 'selected'].includes(prop as string),
})<PriorityProps & { interactive?: boolean; selected?: boolean }>(({ theme, priority, interactive, selected }) => ({
  transition: theme.transitions.create(['transform', 'box-shadow', 'border-color'], {
    duration: theme.transitions.duration.short,
  }),
  ...(priority && {
    borderLeft: `4px solid ${
      priority === 'high' 
        ? theme.palette.error.main
        : priority === 'medium'
        ? theme.palette.warning.main
        : theme.palette.success.main
    }`,
  }),
  ...(selected && {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.selected,
  }),
  ...(interactive && {
    cursor: 'pointer',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[4],
    },
  }),
}));

/**
 * Stat card for dashboard metrics
 */
export const StatCard = styled(Card)(({ theme }) => ({
  height: '100%',
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[3],
  },
}));

/**
 * Stat card content with centered text
 */
export const StatCardContent = styled(CardContent)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(3),
  '&:last-child': {
    paddingBottom: theme.spacing(3),
  },
}));
