// User types
export interface User {
  id: string;
  email: string;
  name: string;
  createdAt?: string;
  updatedAt?: string;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  name: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

// Message types
export interface Message {
  id: string;
  from: string;
  subject: string;
  snippet: string;
  date: string;
  body?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  processed?: boolean;
}

// Activity types
export type ActivityCategory = 'school_event' | 'sports' | 'medical' | 'social' | 'other';
export type ActivityPriority = 'low' | 'medium' | 'high';
export type ActivityStatus = 'upcoming' | 'urgent' | 'scheduled' | 'completed' | 'cancelled';

export interface Activity {
  id: number;
  title: string;
  category: ActivityCategory;
  priority: ActivityPriority;
  date: string;
  child: string;
  status: ActivityStatus;
  description: string;
}

// Reminder types
export interface Reminder {
  id: number;
  text: string;
  priority: ActivityPriority;
  completed?: boolean;
  dueDate?: string;
}

// Dashboard types
export type DashboardTab = 'overview' | 'messages' | 'calendar' | 'family';

// Component prop types
export interface LandingPageProps {
  user?: User | null;
  onShowLogin: () => void;
  onShowRegister?: () => void;
}

export interface LoginProps {
  onLogin: (token: string, user: User) => void;
  onBack: () => void;
}

export interface DashboardProps {
  user: User | null;
  onLogout: () => void;
}

export interface EmailListProps {
  messages: Message[];
  onSelectMessage: (messageId: string) => void;
  selectedMessageId?: string;
}

// API response types
export interface ApiError {
  error: string;
  message?: string;
}

// Event handler types
export type ClickHandler = (event: React.MouseEvent<HTMLButtonElement>) => void;
export type FormSubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void;
export type InputChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;

// Utility types
export type LoadingState = boolean;
export type ErrorState = string | null;
