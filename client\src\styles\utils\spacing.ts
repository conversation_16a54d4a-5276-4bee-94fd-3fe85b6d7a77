import { Theme } from '@mui/material/styles';

// =============================================================================
// SPACING UTILITY FUNCTIONS
// =============================================================================

/**
 * Create responsive spacing values
 */
export const createResponsiveSpacing = (theme: Theme, xs: number, sm?: number, md?: number, lg?: number) => ({
  padding: theme.spacing(xs),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(sm || xs),
  },
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(md || sm || xs),
  },
  [theme.breakpoints.up('lg')]: {
    padding: theme.spacing(lg || md || sm || xs),
  },
});
