import { styled } from '@mui/material/styles';
import { Typography } from '@mui/material';
import { VariantProps, SizeProps } from '../../types/props';

// =============================================================================
// TYPOGRAPHY COMPONENTS
// =============================================================================

/**
 * Enhanced typography with variant-based styling
 */
export const StyledTypography = styled(Typography, {
  shouldForwardProp: (prop) => !['colorVariant'].includes(prop as string),
})<{ colorVariant?: VariantProps['variant'] }>(({ theme, colorVariant }) => ({
  ...(colorVariant && {
    color: theme.palette[colorVariant].main,
  }),
}));

/**
 * Icon typography for emoji icons with consistent sizing
 */
export const IconTypography = styled(Typography, {
  shouldForwardProp: (prop) => !['size'].includes(prop as string),
})<SizeProps>(({ size = 'medium' }) => ({
  fontSize: size === 'small' ? '1.5rem' : size === 'large' ? '4rem' : '2rem',
  lineHeight: 1,
  display: 'block',
}));
