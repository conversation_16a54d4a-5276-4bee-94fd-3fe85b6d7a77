import React from 'react';
import { Typography, Box, Card, CardContent } from '@mui/material';
import AppPage from '../../components/App/AppPage';
import { DashboardProps } from '../../types';

const AnalyticalDashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {

  return (
    <AppPage user={user} onLogout={onLogout} title="Analytical Dashboard">
      <Box>
        <Typography variant="h4" gutterBottom>
          Analytical Dashboard
        </Typography>
        <Card>
          <CardContent>
            <Typography variant="body1">
              This is the analytical dashboard page. Analytics and charts will be added here.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </AppPage>
  );
};

export default AnalyticalDashboard;
