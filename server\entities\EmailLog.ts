import { En<PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from "typeorm"
import { User } from "./User"

@Entity()
export class EmailLog {
    @PrimaryGeneratedColumn()
    id: number

    @Column()
    subject: string

    @Column("text")
    summary: string

    @Column("text")
    fullJsonResponse: string

    @Column()
    fromEmail: string

    @ManyToOne(() => User)
    @JoinColumn({ name: "userId" })
    user: User

    @Column()
    userId: number

    @CreateDateColumn()
    createdAt: Date
}