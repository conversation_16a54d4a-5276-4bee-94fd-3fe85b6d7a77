import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { User } from '../types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  user: User | null;
  token: string | null;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, user, token }) => {
  const location = useLocation();

  if (!token || !user) {
    // Redirect to login page and save the attempted location
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
