import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Tabs,
  Tab,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Grid,
  TextField,
  Box,
  IconButton
} from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { Delete } from '@mui/icons-material';
import { Refresh, AccountCircle } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import AppPage from '../../components/App/AppPage';
import { DashboardProps, Message, Activity, Reminder } from '../../types';
import {
  TabContainer,
  PaddedContent,
  StatsGridContainer,
  CenteredCardContent,
  LargeIconTypography,
  ExtraLargeIconTypography,
  SpacedCard,
  ResponsiveGridContainer,
  CenteredAvatar,
  SpacedTypography,
  FlexContainer,
} from '../../components/styled';

const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [searchText, setSearchText] = useState<string>('');

  const mockActivities: Activity[] = [
    {
      id: 1,
      title: 'Picture Day - Lincoln Elementary',
      category: 'school_event',
      priority: 'medium',
      date: '2024-02-15',
      child: 'Emma',
      status: 'upcoming',
      description: 'School picture day. Remember to dress Emma in her blue dress.'
    },
    {
      id: 2,
      title: 'Soccer Practice Cancelled',
      category: 'sports',
      priority: 'high',
      date: '2024-02-12',
      child: 'Jake',
      status: 'urgent',
      description: 'Due to weather conditions, soccer practice is cancelled today.'
    }
  ];

  const mockReminders: Reminder[] = [
    { id: 1, text: 'Permission slip for field trip due tomorrow', priority: 'high' },
    { id: 2, text: 'Bring snacks for Emma\'s class party on Friday', priority: 'medium' }
  ];

  const fetchMessages = async (): Promise<void> => {
    setLoading(true);
    try {
      const response = await fetch('/api/email/messages', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data: Message[] = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      setMessages([
        {
          id: '1',
          from: 'Lincoln Elementary <<EMAIL>>',
          subject: 'Picture Day Reminder - February 15th',
          snippet: 'Don\'t forget! Picture day is coming up on February 15th...',
          date: '2024-02-10'
        }
      ]);
    }
    setLoading(false);
  };

  const deleteMessage = async (messageId: string): Promise<void> => {
    try {
      const response = await fetch(`/api/email/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        setMessages(messages.filter(m => m.id !== messageId));
      }
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  };



  useEffect(() => {
    fetchMessages();
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      default: return 'success';
    }
  };

  const renderOverview = () => (
    <PaddedContent>
      <StatsGridContainer container spacing={3}>
        {[
          { icon: '📧', number: messages.length.toString(), label: t('dashboard.newMessages') },
          { icon: '⏰', number: '5', label: t('dashboard.upcomingEvents') },
          { icon: '🚨', number: '2', label: t('dashboard.urgentItems') },
          { icon: '👨👩👧👦', number: '2', label: t('dashboard.children') }
        ].map((stat, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card>
              <CenteredCardContent>
                <LargeIconTypography variant="h2">{stat.icon}</LargeIconTypography>
                <Typography variant="h4" component="div">{stat.number}</Typography>
                <Typography variant="body2" color="text.secondary">{stat.label}</Typography>
              </CenteredCardContent>
            </Card>
          </Grid>
        ))}
      </StatsGridContainer>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>{t('dashboard.recentActivities')}</Typography>
              {mockActivities.map(activity => (
                <SpacedCard key={activity.id}>
                  <Card variant="outlined">
                    <CardContent>
                      <FlexContainer justify="space-between" align="center" gap={1}>
                        <Typography variant="h6">{activity.title}</Typography>
                        <Chip
                          label={activity.priority}
                          color={getPriorityColor(activity.priority) as any}
                          size="small"
                        />
                      </FlexContainer>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        👤 {activity.child} • 📅 {activity.date}
                      </Typography>
                      <Typography variant="body2">{activity.description}</Typography>
                    </CardContent>
                  </Card>
                </SpacedCard>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>{t('dashboard.quickReminders')}</Typography>
              <List>
                {mockReminders.map(reminder => (
                  <ListItem key={reminder.id} divider>
                    <ListItemText primary={reminder.text} />
                    <ListItemSecondaryAction>
                      <Button size="small" variant="outlined">{t('dashboard.done')}</Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </PaddedContent>
  );

  return (
    <AppPage user={user} onLogout={onLogout} title="Dashboard">
      <TabContainer>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label={`📊 ${t('dashboard.overview')}`} />
          <Tab label={`📧 ${t('dashboard.messages')}`} />
          <Tab label={`📅 ${t('dashboard.calendar')}`} />
          <Tab label={`👨👩👧👦 ${t('dashboard.family')}`} />
        </Tabs>
      </TabContainer>

      {activeTab === 0 && renderOverview()}

      {activeTab === 1 && (
        <PaddedContent>
          <FlexContainer justify="space-between" align="center" gap={3}>
            <Typography variant="h5">{t('dashboard.allMessages')}</Typography>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={fetchMessages}
              disabled={loading}
            >
              {loading ? t('dashboard.loading') : t('dashboard.refresh')}
            </Button>
          </FlexContainer>

          <TextField
            label="Search messages"
            variant="outlined"
            size="small"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ marginTop: '16px', marginBottom: '8px', width: '300px' }}
          />
          
          <div style={{ height: 600, width: '100%', marginTop: '8px' }}>
            <DataGrid
              rows={messages
                .filter(msg => 
                  !searchText || 
                  msg.from.toLowerCase().includes(searchText.toLowerCase()) ||
                  msg.subject.toLowerCase().includes(searchText.toLowerCase()) ||
                  msg.snippet.toLowerCase().includes(searchText.toLowerCase())
                )
                .map(msg => ({
                  id: msg.id,
                  from: msg.from,
                  subject: msg.subject,
                  summary: msg.snippet,
                  rawJson: msg.body || 'N/A',
                  date: msg.date,
                  dateFormatted: new Date(msg.date).toLocaleDateString()
                }))}
              columns={[
                { field: 'from', headerName: t('dashboard.from'), width: 200 },
                { field: 'subject', headerName: t('dashboard.subject'), width: 250 },
                { 
                  field: 'summary', 
                  headerName: t('dashboard.summary'), 
                  width: 300,
                  renderCell: (params) => (
                    <Box sx={{ 
                      whiteSpace: 'pre-wrap',
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'flex-start',
                      py: 1
                    }}>
                      {params.value}
                    </Box>
                  )
                },
                { 
                  field: 'rawJson', 
                  headerName: t('dashboard.rawJson'), 
                  width: 400, 
                  sortable: false,
                  renderCell: (params) => (
                    <Box sx={{ 
                      fontSize: '0.75rem', 
                      fontFamily: 'monospace', 
                      whiteSpace: 'pre-wrap',
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'flex-start',
                      py: 1,
                      overflow: 'auto'
                    }}>
                      {params.value}
                    </Box>
                  )
                },
                { field: 'dateFormatted', headerName: t('dashboard.date'), width: 120 },
                {
                  field: 'actions',
                  headerName: '',
                  width: 80,
                  sortable: false,
                  renderCell: (params) => (
                    <IconButton
                      size="small"
                      onClick={() => deleteMessage(params.row.id)}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  )
                }
              ] as GridColDef[]}
              initialState={{
                pagination: { paginationModel: { pageSize: 25 } },
                sorting: { sortModel: [{ field: 'date', sort: 'desc' }] }
              }}
              pageSizeOptions={[10, 25, 50]}
              disableRowSelectionOnClick
              getRowHeight={() => 'auto'}
            />
          </div>
        </PaddedContent>
      )}

      {activeTab === 2 && (
        <PaddedContent textAlign="center">
          <Card>
            <CardContent>
              <ExtraLargeIconTypography variant="h2">📅</ExtraLargeIconTypography>
              <Typography variant="h5" gutterBottom>Calendar View</Typography>
              <Typography variant="body1" color="text.secondary">
                Calendar integration coming soon. View all your family's events and deadlines in one place.
              </Typography>
            </CardContent>
          </Card>
        </PaddedContent>
      )}

      {activeTab === 3 && (
        <PaddedContent textAlign="center">
          <Card>
            <CardContent>
              <ExtraLargeIconTypography variant="h2">👨👩👧👦</ExtraLargeIconTypography>
              <Typography variant="h5" gutterBottom>Family Management</Typography>
              <SpacedTypography variant="body1" color="text.secondary">
                Manage family members, children profiles, and sharing settings.
              </SpacedTypography>
              <ResponsiveGridContainer>
                {[
                  { name: 'Emma (Age 8)', school: 'Lincoln Elementary - 3rd Grade' },
                  { name: 'Jake (Age 12)', school: 'Roosevelt Middle School - 7th Grade' }
                ].map((child, index) => (
                  <Card key={index} variant="outlined">
                    <CardContent>
                      <CenteredAvatar>
                        <AccountCircle />
                      </CenteredAvatar>
                      <Typography variant="h6">{child.name}</Typography>
                      <Typography variant="body2" color="text.secondary">{child.school}</Typography>
                    </CardContent>
                  </Card>
                ))}
              </ResponsiveGridContainer>
            </CardContent>
          </Card>
        </PaddedContent>
      )}
    </AppPage>
  );
};

export default Dashboard;