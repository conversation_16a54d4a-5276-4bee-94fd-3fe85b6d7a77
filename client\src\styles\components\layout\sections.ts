import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';
import { FlexContainer } from './containers';

// =============================================================================
// LAYOUT SECTION COMPONENTS
// =============================================================================

// Section Props Interface
interface SectionProps {
  spacing?: 'small' | 'medium' | 'large' | 'xlarge';
  background?: 'default' | 'paper' | 'primary' | 'secondary';
  textAlign?: 'left' | 'center' | 'right';
}

/**
 * Section wrapper with consistent spacing and background options
 */
export const Section = styled(Box, {
  shouldForwardProp: (prop) => !['spacing', 'background', 'textAlign'].includes(prop as string),
})<SectionProps>(({ theme, spacing = 'medium', background, textAlign }) => {
  const getSpacing = () => {
    switch (spacing) {
      case 'small':
        return theme.spacing(4, 0);
      case 'large':
        return theme.spacing(10, 0);
      case 'xlarge':
        return theme.spacing(12, 0);
      default:
        return theme.spacing(8, 0);
    }
  };

  const getBackground = () => {
    switch (background) {
      case 'paper':
        return theme.palette.background.paper;
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      default:
        return 'transparent';
    }
  };

  return {
    padding: getSpacing(),
    backgroundColor: getBackground(),
    textAlign: textAlign || 'inherit',
    position: 'relative',
    zIndex: 1,
    ...(background === 'primary' && {
      color: theme.palette.primary.contrastText,
    }),
    ...(background === 'secondary' && {
      color: theme.palette.secondary.contrastText,
    }),
  };
});

/**
 * Page header with title and actions
 */
export const PageHeader = styled(FlexContainer)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: theme.spacing(2),
  },
}));

/**
 * Empty state container
 */
export const EmptyState = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(6, 3),
  color: theme.palette.text.secondary,
}));
