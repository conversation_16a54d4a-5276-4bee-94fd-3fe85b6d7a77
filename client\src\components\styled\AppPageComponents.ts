import { styled } from '@mui/material/styles';
import { Box, AppBar, Typography, IconButton } from '@mui/material';

// =============================================================================
// APP PAGE LAYOUT COMPONENTS
// =============================================================================

// Main Layout Container - Flex container for app layout
export const MainLayoutContainer = styled(Box)(() => ({
  display: 'flex',
}));

// Styled App Bar - App bar with dynamic margin based on drawer state
interface StyledAppBarProps {
  open?: boolean;
}

export const StyledAppBar = styled(AppBar, {
  shouldForwardProp: (prop) => !['open'].includes(prop as string),
})<StyledAppBarProps>(({ theme, open }) => ({
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    transition: theme.transitions.create(['margin', 'width'], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

// App Bar Actions Container - Flex container for app bar actions
export const AppBarActionsContainer = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: 8, // 1 spacing unit = 8px by default
}));

// Menu Button - Icon button with conditional display and margin
interface MenuButtonProps {
  marginRight?: number;
  hideWhenOpen?: boolean;
  drawerOpen?: boolean;
  isMobile?: boolean;
}

export const MenuButton = styled(IconButton, {
  shouldForwardProp: (prop) => !['marginRight', 'hideWhenOpen', 'drawerOpen', 'isMobile'].includes(prop as string),
})<MenuButtonProps>(({ theme, marginRight = 2, hideWhenOpen, drawerOpen, isMobile }) => ({
  marginRight: theme.spacing(marginRight),
  ...(hideWhenOpen && drawerOpen && !isMobile && {
    display: 'none',
  }),
}));

// App Title Typography - Typography with flex grow and font weight
export const AppTitleTypography = styled(Typography)(() => ({
  flexGrow: 1,
  fontWeight: 600,
}));

// Navigation Components
// Logo Image - Styled image component for navigation logo
interface LogoImageProps {
  size?: number;
  marginRight?: number;
}

export const LogoImage = styled('img')<LogoImageProps>(({ theme, size = 32, marginRight = 1 }) => ({
  width: size,
  height: size,
  marginRight: theme.spacing(marginRight),
  cursor: 'pointer',
}));

// Navigation Title - Typography with flex grow and cursor pointer
export const NavigationTitle = styled(Typography)(() => ({
  flexGrow: 1,
  cursor: 'pointer',
}));

// Breadcrumbs Container - Container with padding and background
export const BreadcrumbsContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
}));

// Breadcrumb Link - Link with flex display and hover effects
export const BreadcrumbLink = styled('a')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  textDecoration: 'none',
  color: 'inherit',
  '&:hover': {
    textDecoration: 'underline',
  },
}));

// Navigation Avatar - Avatar with specific size for navigation
interface NavigationAvatarProps {
  size?: number;
}

export const NavigationAvatar = styled(Box)<NavigationAvatarProps>(({ size = 32 }) => ({
  width: size,
  height: size,
}));
