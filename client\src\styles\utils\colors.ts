import { Theme } from '@mui/material/styles';

// =============================================================================
// COLOR UTILITY FUNCTIONS
// =============================================================================

/**
 * Get priority color from theme
 */
export const getPriorityColor = (theme: Theme, priority: string) => {
  switch (priority) {
    case 'high':
      return theme.palette.error.main;
    case 'medium':
      return theme.palette.warning.main;
    case 'low':
      return theme.palette.success.main;
    default:
      return theme.palette.text.secondary;
  }
};
