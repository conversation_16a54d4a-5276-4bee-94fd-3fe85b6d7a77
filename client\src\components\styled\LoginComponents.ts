import { styled } from '@mui/material/styles';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Link,
} from '@mui/material';
import {
  createHoverEffect,
  createGlassMorphism,
  extendedShadows,
  animations,
} from '../../styles/themeExtensions';

// Login Page Container with gradient background
export const LoginPageContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  background: `linear-gradient(135deg, 
    ${theme.palette.primary.light}20 0%, 
    ${theme.palette.secondary.light}20 50%, 
    ${theme.palette.primary.main}10 100%
  )`,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 20% 80%, ${theme.palette.primary.main}15 0%, transparent 50%),
                 radial-gradient(circle at 80% 20%, ${theme.palette.secondary.main}15 0%, transparent 50%)`,
    pointerEvents: 'none',
  },
}));

// Login Container
export const LoginContainer = styled(Container)(() => ({
  position: 'relative',
  zIndex: 1,
}));

// Login Card with glass morphism effect
export const LoginCard = styled(Card)(({ theme }) => ({
  ...createGlassMorphism(theme, 0.1),
  backdropFilter: 'blur(20px)',
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: extendedShadows.modal,
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  ...createHoverEffect(theme, 'subtle'),
}));

// Login Card Content
export const LoginCardContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(4),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(5),
  },
}));

// Login Header Container
export const LoginHeader = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  marginBottom: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

// Logo Container with hover effect
export const LoginLogo = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  marginBottom: theme.spacing(1),
  transition: theme.transitions.create(['transform'], {
    duration: animations.duration.normal,
  }),
  '&:hover': {
    transform: 'scale(1.05) rotate(5deg)',
  },
}));

// Login Title
export const LoginTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  marginBottom: theme.spacing(1),
}));

// Login Subtitle
export const LoginSubtitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
  marginBottom: theme.spacing(1),
}));

// Login Description
export const LoginDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  lineHeight: 1.6,
}));

// Login Form Container
export const LoginForm = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

// Enhanced TextField with focus states
export const LoginTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.spacing(1),
    transition: theme.transitions.create(['border-color', 'box-shadow', 'transform'], {
      duration: animations.duration.fast,
    }),
    '&:hover': {
      transform: 'translateY(-1px)',
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.main,
      },
    },
    '&.Mui-focused': {
      transform: 'translateY(-1px)',
      boxShadow: `0 4px 12px ${theme.palette.primary.main}25`,
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.main,
        borderWidth: 2,
      },
    },
    '&.Mui-error': {
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.error.main,
      },
      '&.Mui-focused': {
        boxShadow: `0 4px 12px ${theme.palette.error.main}25`,
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.error.main,
        },
      },
    },
  },
  '& .MuiInputLabel-root': {
    '&.Mui-focused': {
      color: theme.palette.primary.main,
    },
    '&.Mui-error': {
      '&.Mui-focused': {
        color: theme.palette.error.main,
      },
    },
  },
}));

// Enhanced Submit Button
export const LoginSubmitButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1.5, 3),
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
  boxShadow: `0 4px 12px ${theme.palette.primary.main}40`,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
    transform: 'translateY(-2px)',
    boxShadow: `0 6px 20px ${theme.palette.primary.main}50`,
  },
  '&:active': {
    transform: 'translateY(0)',
  },
  '&.Mui-disabled': {
    background: theme.palette.action.disabledBackground,
    color: theme.palette.action.disabled,
    boxShadow: 'none',
    transform: 'none',
  },
}));

// Error Alert with enhanced styling
export const LoginAlert = styled(Alert)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  '&.MuiAlert-standardError': {
    backgroundColor: `${theme.palette.error.main}10`,
    border: `1px solid ${theme.palette.error.main}30`,
    '& .MuiAlert-icon': {
      color: theme.palette.error.main,
    },
  },
}));

// Login Footer Container
export const LoginFooter = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  marginTop: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

// Toggle Link with hover effect
export const LoginToggleLink = styled(Link)(({ theme }) => ({
  cursor: 'pointer',
  marginLeft: theme.spacing(0.5),
  fontWeight: 600,
  color: theme.palette.primary.main,
  textDecoration: 'none',
  transition: theme.transitions.create(['color', 'transform'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    color: theme.palette.primary.dark,
    transform: 'translateY(-1px)',
    textDecoration: 'underline',
  },
}));

// Back Button with subtle styling
export const LoginBackButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  textTransform: 'none',
  color: theme.palette.text.secondary,
  borderColor: theme.palette.divider,
  transition: theme.transitions.create(['color', 'border-color', 'transform'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    color: theme.palette.text.primary,
    borderColor: theme.palette.text.secondary,
    transform: 'translateY(-1px)',
    backgroundColor: 'transparent',
  },
}));

// Loading state overlay
export const LoginLoadingOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: `${theme.palette.background.paper}80`,
  backdropFilter: 'blur(2px)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: 'inherit',
  zIndex: 10,
}));
