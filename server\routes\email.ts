import express, { Request, Response } from 'express';
import { authenticateToken } from '../middleware/auth';
import { AppDataSource } from '../data-source';
import { EmailLog } from '../entities/EmailLog';

const router = express.Router();

router.get('/messages', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const emailLogRepository = AppDataSource.getRepository(EmailLog);
    
    const emailLogs = await emailLogRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' }
    });

    const messages = emailLogs.map(log => ({
      id: log.id.toString(),
      from: log.fromEmail,
      subject: log.subject,
      snippet: log.summary,
      date: log.createdAt.toISOString(),
      body: log.fullJsonResponse
    }));

    res.json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

router.delete('/messages/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const messageId = req.params.id;
    const emailLogRepository = AppDataSource.getRepository(EmailLog);
    
    const result = await emailLogRepository.delete({ id: parseInt(messageId), userId });
    
    if (result.affected === 0) {
      return res.status(404).json({ error: 'Message not found' });
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({ error: 'Failed to delete message' });
  }
});

export default router;