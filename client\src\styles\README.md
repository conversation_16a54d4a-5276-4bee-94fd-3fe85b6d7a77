# Enhanced MUI Theme System

This directory contains the enhanced Material-UI theme system for the Pigeon Squad application, built with a fully styled components-based architecture for optimal performance and maintainability.

## 📁 File Structure

```
styles/
├── README.md                 # This documentation
├── index.ts                  # Main exports for the modular structure
├── themeExtensions.ts       # Theme extensions and custom properties
├── components/              # Styled components organized by category
│   ├── index.ts            # Component exports
│   ├── layout/             # Layout components
│   │   ├── index.ts       # Layout exports
│   │   ├── containers.ts  # StyledContainer, FlexContainer, GridContainer
│   │   └── sections.ts    # Section, PageHeader, EmptyState
│   ├── cards/             # Card components
│   │   ├── index.ts       # Card exports
│   │   └── base.ts        # StyledCard, StatCard, StatCardContent
│   └── typography/        # Typography components
│       ├── index.ts       # Typography exports
│       └── enhanced.ts    # StyledTypography, IconTypography
├── utils/                  # Utility functions
│   ├── index.ts           # Utility exports
│   ├── colors.ts          # getPriorityColor
│   ├── spacing.ts         # createResponsiveSpacing
│   └── effects.ts         # createHoverEffect, createFocusRing
└── types/                  # TypeScript type definitions
    ├── index.ts           # Type exports
    └── props.ts           # SpacingProps, VariantProps, etc.
```

## 🎨 Theme Architecture

### Core Theme (`theme.ts`)
- **Base Theme**: Material-UI v7 theme with custom palette, typography, and spacing
- **Extended Properties**: Custom theme properties for priority colors, shadows, and animations
- **Component Overrides**: Enhanced default styling for all MUI components

### Modular Styled Components (`components/`)
- **Layout Components**: Containers, sections, and layout utilities for consistent spacing and structure
- **Card Components**: Enhanced cards with priority styling, hover effects, and interactive states
- **Typography Components**: Styled typography with variant-based coloring and icon typography

### Utility Functions (`utils/`)
- **Color Utilities**: Priority-based color functions and theme color helpers
- **Spacing Utilities**: Responsive spacing functions for consistent layouts
- **Effect Utilities**: Hover effects, focus rings, and interactive state styling

### Type Definitions (`types/`)
- **Component Props**: TypeScript interfaces for all styled component props
- **Theme Types**: Extended theme type definitions for custom properties

### Theme Extensions (`themeExtensions.ts`)
- **Priority Colors**: High, medium, low priority color system
- **Status Colors**: Success, warning, error, info with extended shades
- **Extended Shadows**: Card, hover, elevated, dropdown, modal shadows
- **Animations**: Fade, slide, pulse, and other animation configurations
- **Legacy Utility Functions**: Helper functions for responsive values and state styling

## 🚀 Usage Examples

### Using Modular Styled Components (Recommended)

```tsx
// Import from main styles export
import {
  StyledContainer,
  FlexContainer,
  GridContainer,
  StyledCard,
  StyledTypography,
  getPriorityColor
} from '@/styles';

// Or import from specific modules
import { StyledContainer, FlexContainer } from '@/styles/components/layout';
import { StyledCard } from '@/styles/components/cards';
import { getPriorityColor } from '@/styles/utils';

function MyComponent() {
  return (
    <StyledContainer spacing={3}>
      <FlexContainer justify="space-between" align="center" gap={2}>
        <StyledTypography variant="h5" colorVariant="primary">
          Dashboard Overview
        </StyledTypography>
        <Button variant="contained">Refresh</Button>
      </FlexContainer>

      <GridContainer columns={{ xs: 1, sm: 2, md: 3 }} gap={3}>
        <StyledCard interactive priority="high">
          <CardContent>
            <Typography>Interactive high-priority card</Typography>
          </CardContent>
        </StyledCard>
      </GridContainer>
    </StyledContainer>
  );
}
```

### Using Theme Extensions in Custom Styled Components

```tsx
import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

const CustomCard = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.custom.shadows.card,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),

  // Priority styling
  borderLeft: `4px solid ${theme.custom.priority.high.main}`,

  // Hover effects
  '&:hover': {
    boxShadow: theme.custom.shadows.cardHover,
    transform: 'translateY(-2px)',
  },
}));
```

### Using Styled Components

```tsx
import { styled } from '@mui/material/styles';
import { Button, Card, Typography } from '@mui/material';
import { extendedShadows } from '../styles/themeExtensions';

// Create custom styled components instead of using variants
const GradientButton = styled(Button)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: theme.palette.common.white,
  '&:hover': {
    background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
  },
}));

const ElevatedCard = styled(Card)(() => ({
  boxShadow: extendedShadows.cardElevated,
  '&:hover': {
    boxShadow: extendedShadows.modal,
    transform: 'translateY(-2px)',
  },
}));

function ExampleComponent() {
  return (
    <>
      <GradientButton>Gradient Button</GradientButton>
      <ElevatedCard>Elevated Card</ElevatedCard>
    </>
  );
}
```

### Using Utility Functions

```tsx
import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

// Import from modular utils
import {
  getPriorityColor,
  createResponsiveSpacing,
  createHoverEffect,
  createFocusRing
} from '@/styles/utils';

// Or import from theme extensions (legacy)
import {
  createResponsiveValue,
  createHoverState
} from '@/styles/themeExtensions';

const ResponsiveBox = styled(Box)(({ theme }) => ({
  // New modular utility functions
  ...createResponsiveSpacing(theme, 2, 3, 4, 5), // xs, sm, md, lg
  ...createHoverEffect(theme, 'medium'),
  ...createFocusRing(theme),
  borderColor: getPriorityColor(theme, 'high'),

  // Legacy theme extension functions (still available)
  padding: createResponsiveValue(theme, 2, 4),
  ...createHoverState(theme, 'medium'),
}));
```

## 🎯 Design System Features

### Color System
- **Primary**: Blue (#3B82F6) with light and dark variants
- **Secondary**: Purple (#8B5CF6) with light and dark variants
- **Priority Colors**: High (red), Medium (orange), Low (green)
- **Status Colors**: Success, warning, error, info with extended palettes
- **Neutral Colors**: Comprehensive gray scale

### Typography Scale
- **Font Family**: Inter, Roboto, Helvetica, Arial
- **Scale**: 6 heading levels + body text + captions
- **Custom Variants**: Stat numbers, card titles, gradient text
- **Responsive**: Automatic scaling on mobile devices

### Spacing System
- **Base Unit**: 8px
- **Scale**: 0.5x to 8x multipliers (4px to 64px)
- **Responsive**: Automatic adjustment for different screen sizes

### Shadow System
- **Card**: Subtle shadow for cards and panels
- **Card Hover**: Enhanced shadow for interactive states
- **Card Elevated**: Strong shadow for important elements
- **Dropdown**: Shadow for floating elements
- **Modal**: Deep shadow for overlays

### Animation System
- **Duration**: Short (150ms), standard (300ms), complex (500ms)
- **Easing**: Standard Material Design curves
- **Presets**: Fade in, slide in, pulse, hover effects

## 🔧 Customization

### Adding New Priority Colors

```tsx
// In themeExtensions.ts
export const priorityColors = {
  // ... existing colors
  critical: {
    main: '#DC2626',
    light: '#FCA5A5',
    dark: '#991B1B',
    contrastText: '#FFFFFF',
  },
};
```

### Creating Custom Styled Components

```tsx
// Create reusable styled components instead of variants
import { styled } from '@mui/material/styles';
import { Button } from '@mui/material';

export const CustomButton = styled(Button)(({ theme }) => ({
  backgroundColor: theme.palette.info.main,
  color: theme.palette.info.contrastText,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(1.5, 3),
  '&:hover': {
    backgroundColor: theme.palette.info.dark,
  },
}));
```

### Extending Theme Properties

```tsx
// In theme.ts - Module augmentation
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      // ... existing properties
      newProperty: {
        value: string;
      };
    };
  }
}
```

## 📱 Responsive Design

The theme includes comprehensive responsive design support:

- **Breakpoints**: xs (0px), sm (600px), md (900px), lg (1200px), xl (1536px)
- **Responsive Utilities**: Functions for responsive spacing, typography, and layout
- **Mobile-First**: All responsive utilities use mobile-first approach
- **Container Queries**: Support for container-based responsive design

## 🧪 Testing

The theme system includes proper TypeScript support and testing utilities:

- **Type Safety**: Full TypeScript support for all custom properties
- **Theme Provider**: Proper theme context for testing
- **Mock Theme**: Simplified theme for unit tests
- **Visual Regression**: Support for visual testing tools

## 🔄 Migration Guide

### From Legacy styledUtils.ts (Completed)

The monolithic `styledUtils.ts` file has been successfully migrated to a modular structure. All imports now use the new paths:

**Old Import (Deprecated - Removed):**
```tsx
// ❌ This no longer works
import { StyledContainer, getPriorityColor } from '@/styles/styledUtils';
```

**New Imports (Current):**
```tsx
// ✅ Recommended: Import from main styles export
import { StyledContainer, getPriorityColor } from '@/styles';

// ✅ Alternative: Import from specific modules
import { StyledContainer } from '@/styles/components/layout';
import { getPriorityColor } from '@/styles/utils';
```

### Component Migration Examples

**Layout Components:**
```tsx
// All layout components now available from:
import {
  StyledContainer,
  FlexContainer,
  GridContainer,
  Section,
  PageHeader,
  EmptyState
} from '@/styles/components/layout';
```

**Card Components:**
```tsx
// All card components now available from:
import {
  StyledCard,
  StatCard,
  StatCardContent
} from '@/styles/components/cards';
```

**Typography Components:**
```tsx
// All typography components now available from:
import {
  StyledTypography,
  IconTypography
} from '@/styles/components/typography';
```

**Utility Functions:**
```tsx
// All utility functions now available from:
import {
  getPriorityColor,
  createResponsiveSpacing,
  createHoverEffect,
  createFocusRing
} from '@/styles/utils';
```

## 🚀 Performance

The enhanced theme system is optimized for performance:

- **Build-Time Extraction**: Styles are extracted at build time when possible
- **Minimal Runtime**: Reduced runtime CSS-in-JS overhead
- **Tree Shaking**: Unused theme properties are removed in production
- **Caching**: Proper caching of computed styles

## 📚 Resources

- [Material-UI Theme Documentation](https://mui.com/material-ui/customization/theming/)
- [Styled Components Guide](https://mui.com/system/styled/)
- [Component Variants](https://mui.com/material-ui/customization/theme-components/#creating-new-component-variants)
- [TypeScript Support](https://mui.com/material-ui/guides/typescript/)
