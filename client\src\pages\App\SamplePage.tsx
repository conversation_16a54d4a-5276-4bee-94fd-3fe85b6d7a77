import React from 'react';
import { Typo<PERSON>, Card, CardContent, Button } from '@mui/material';
import AppPage from '../../components/App/AppPage';
import { DashboardProps } from '../../types';
import { FlexContainer, Section } from '../../components/styled';
import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

// Styled component for flexible card container
const FlexCardContainer = styled(Box)(() => ({
  flex: '1 1 300px',
  minWidth: 300,
}));

const SamplePage: React.FC<DashboardProps> = ({ user, onLogout }) => {
  return (
    <AppPage user={user} onLogout={onLogout} title="Sample Page">
      <Section>
        <Typography variant="h4" gutterBottom>
          Sample Page
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          This is a sample page to demonstrate the navigation system.
        </Typography>

        <FlexContainer direction="row" gap={3} wrap={true}>
          <FlexCardContainer>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sample Card 1
                </Typography>
                <Typography variant="body2" paragraph>
                  This is a sample card with some content to show how the layout works.
                </Typography>
                <Button variant="contained" color="primary">
                  Sample Action
                </Button>
              </CardContent>
            </Card>
          </FlexCardContainer>
          <FlexCardContainer>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sample Card 2
                </Typography>
                <Typography variant="body2" paragraph>
                  Another sample card to demonstrate the grid layout.
                </Typography>
                <Button variant="outlined" color="secondary">
                  Another Action
                </Button>
              </CardContent>
            </Card>
          </FlexCardContainer>
        </FlexContainer>
      </Section>
    </AppPage>
  );
};

export default SamplePage;
