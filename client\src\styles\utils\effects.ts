import { Theme } from '@mui/material/styles';

// =============================================================================
// VISUAL EFFECTS UTILITY FUNCTIONS
// =============================================================================

/**
 * Create hover effect styles
 */
export const createHoverEffect = (theme: Theme, intensity: 'subtle' | 'medium' | 'strong' = 'medium') => {
  const transforms = {
    subtle: 'translateY(-1px)',
    medium: 'translateY(-2px)',
    strong: 'translateY(-4px)',
  };
  
  const shadows = {
    subtle: theme.shadows[2],
    medium: theme.shadows[4],
    strong: theme.shadows[8],
  };

  return {
    transition: theme.transitions.create(['transform', 'box-shadow'], {
      duration: theme.transitions.duration.short,
    }),
    '&:hover': {
      transform: transforms[intensity],
      boxShadow: shadows[intensity],
    },
  };
};

/**
 * Create focus ring styles
 */
export const createFocusRing = (theme: Theme, color?: string) => ({
  '&:focus-visible': {
    outline: `2px solid ${color || theme.palette.primary.main}`,
    outlineOffset: '2px',
  },
});
