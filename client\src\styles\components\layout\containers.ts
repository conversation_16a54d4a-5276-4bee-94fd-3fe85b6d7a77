import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';
import { SpacingProps } from '../../types/props';

// =============================================================================
// LAYOUT CONTAINER COMPONENTS
// =============================================================================

/**
 * Flexible container with consistent padding and spacing
 */
export const StyledContainer = styled(Box, {
  shouldForwardProp: (prop) => !['spacing', 'gap'].includes(prop as string),
})<SpacingProps>(({ theme, spacing = 3, gap }) => ({
  padding: theme.spacing(spacing),
  ...(gap && {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(typeof gap === 'string' ? parseInt(gap) : gap),
  }),
}));

/**
 * Flex container with customizable direction and spacing
 */
export const FlexContainer = styled(Box, {
  shouldForwardProp: (prop) => !['direction', 'justify', 'align', 'gap', 'wrap'].includes(prop as string),
})<{
  direction?: 'row' | 'column';
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  gap?: number;
  wrap?: boolean;
}>(({ theme, direction = 'row', justify = 'flex-start', align = 'stretch', gap = 0, wrap = false }) => ({
  display: 'flex',
  flexDirection: direction,
  justifyContent: justify,
  alignItems: align,
  gap: gap ? theme.spacing(gap) : 0,
  flexWrap: wrap ? 'wrap' : 'nowrap',
}));

/**
 * Grid container with responsive columns
 */
export const GridContainer = styled(Box, {
  shouldForwardProp: (prop) => !['columns', 'gap', 'minWidth'].includes(prop as string),
})<{
  columns?: { xs?: number; sm?: number; md?: number; lg?: number };
  gap?: number;
  minWidth?: string;
}>(({ theme, columns = { xs: 1, sm: 2, md: 3, lg: 4 }, gap = 2, minWidth }) => ({
  display: 'grid',
  gap: theme.spacing(gap),
  gridTemplateColumns: `repeat(${columns.xs || 1}, 1fr)`,
  ...(minWidth && {
    gridTemplateColumns: `repeat(auto-fit, minmax(${minWidth}, 1fr))`,
  }),
  [theme.breakpoints.up('sm')]: {
    gridTemplateColumns: `repeat(${columns.sm || columns.xs || 1}, 1fr)`,
  },
  [theme.breakpoints.up('md')]: {
    gridTemplateColumns: `repeat(${columns.md || columns.sm || columns.xs || 1}, 1fr)`,
  },
  [theme.breakpoints.up('lg')]: {
    gridTemplateColumns: `repeat(${columns.lg || columns.md || columns.sm || columns.xs || 1}, 1fr)`,
  },
}));
