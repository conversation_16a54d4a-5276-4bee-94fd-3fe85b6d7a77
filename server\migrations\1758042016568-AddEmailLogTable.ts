import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmailLogTable1758042016568 implements MigrationInterface {
    name = 'AddEmailLogTable1758042016568'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "email_log" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "subject" varchar NOT NULL, "summary" text NOT NULL, "fullJsonResponse" text NOT NULL, "fromEmail" varchar NOT NULL, "userId" integer NOT NULL, "createdAt" datetime NOT NULL DEFAULT (datetime('now')))`);
        await queryRunner.query(`CREATE TABLE "temporary_email_log" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "subject" varchar NOT NULL, "summary" text NOT NULL, "fullJsonResponse" text NOT NULL, "fromEmail" varchar NOT NULL, "userId" integer NOT NULL, "createdAt" datetime NOT NULL DEFAULT (datetime('now')), CONSTRAINT "FK_d47ddde257c2b89e2d2be7d6af4" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION)`);
        await queryRunner.query(`INSERT INTO "temporary_email_log"("id", "subject", "summary", "fullJsonResponse", "fromEmail", "userId", "createdAt") SELECT "id", "subject", "summary", "fullJsonResponse", "fromEmail", "userId", "createdAt" FROM "email_log"`);
        await queryRunner.query(`DROP TABLE "email_log"`);
        await queryRunner.query(`ALTER TABLE "temporary_email_log" RENAME TO "email_log"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "email_log" RENAME TO "temporary_email_log"`);
        await queryRunner.query(`CREATE TABLE "email_log" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "subject" varchar NOT NULL, "summary" text NOT NULL, "fullJsonResponse" text NOT NULL, "fromEmail" varchar NOT NULL, "userId" integer NOT NULL, "createdAt" datetime NOT NULL DEFAULT (datetime('now')))`);
        await queryRunner.query(`INSERT INTO "email_log"("id", "subject", "summary", "fullJsonResponse", "fromEmail", "userId", "createdAt") SELECT "id", "subject", "summary", "fullJsonResponse", "fromEmail", "userId", "createdAt" FROM "temporary_email_log"`);
        await queryRunner.query(`DROP TABLE "temporary_email_log"`);
        await queryRunner.query(`DROP TABLE "email_log"`);
    }

}
