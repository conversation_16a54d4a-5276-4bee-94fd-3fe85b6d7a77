import express, { Request, Response } from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';
import AppDataSource from './db';

dotenv.config();

import authRoutes from './routes/auth';
import emailRoutes from './routes/email';
import ImapMonitorService from './services/imapMonitor';

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../../client/dist')));

app.use('/api/auth', authRoutes);
app.use('/api/email', emailRoutes);

app.get('*', (req: Request, res: Response) => {
  res.sendFile(path.join(__dirname, '../../client/dist/index.html'));
});

// Initialize IMAP email monitor service
const imapMonitor = new ImapMonitorService();

AppDataSource.initialize().then(async () => {
  console.log('\x1b[36m📊 Database connected successfully\x1b[0m');
  
  // Run pending migrations
  await AppDataSource.runMigrations();
  console.log('\x1b[36m📋 Migrations applied successfully\x1b[0m');
  
  app.listen(PORT, () => {
    console.log(`\x1b[32m🚀 Server running on port ${PORT}\x1b[0m`);
    
    // Start IMAP monitoring after server starts
    imapMonitor.start();
  });
}).catch(error => {
  console.error('\x1b[31m❌ Database connection failed:\x1b[0m', error);
});

// Graceful shutdown
const shutdown = () => {
  console.log('\n\x1b[33m⏳ Shutting down gracefully...\x1b[0m');
  imapMonitor.stop();
  console.log('\x1b[32m✅ Server shutdown successfully\x1b[0m');
  process.exit(0);
};

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);
process.on('SIGQUIT', shutdown);
process.on('SIGHUP', shutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('\x1b[31m❌ Uncaught Exception:\x1b[0m', err);
  shutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\x1b[31m❌ Unhandled Rejection at:\x1b[0m', promise, 'reason:', reason);
  shutdown();
});