import React from 'react';
import { EmailListProps } from '../../types';
import {
  EmailListContainer,
  EmailList as Styled<PERSON><PERSON><PERSON>ist,
  EmailItem,
  EmailItemButton,
  EmailIconContainer,
  EmailIcon,
  EmailContent,
  EmailHeader,
  EmailSubject,
  EmailMeta,
  EmailFrom,
  EmailDate,
  EmailSnippet,
  EmailTagsContainer,
  EmailTag,
  EmailEmptyStateContainer,
  EmailEmptyStateIcon,
  EmailEmptyStateTitle,
  EmailEmptyStateDescription,
} from '../../components/styled';

const EmailList: React.FC<EmailListProps> = ({ messages, onSelectMessage, selectedMessageId }) => {
  if (!messages || messages.length === 0) {
    return (
      <EmailListContainer>
        <EmailEmptyStateContainer>
          <EmailEmptyStateIcon>📧</EmailEmptyStateIcon>
          <EmailEmptyStateTitle variant="h6">No messages found</EmailEmptyStateTitle>
          <EmailEmptyStateDescription variant="body2">
            Your processed emails will appear here
          </EmailEmptyStateDescription>
        </EmailEmptyStateContainer>
      </EmailListContainer>
    );
  }



  const getCategoryIcon = (subject: string): string => {
    const lowerSubject = subject.toLowerCase();
    if (lowerSubject.includes('school') || lowerSubject.includes('picture')) {
      return '🏫';
    }
    if (lowerSubject.includes('soccer') || lowerSubject.includes('sports')) {
      return '⚽';
    }
    if (lowerSubject.includes('medical') || lowerSubject.includes('health')) {
      return '🏥';
    }
    return '📧';
  };

  const getTags = (subject: string) => {
    const lowerSubject = subject.toLowerCase();
    const tags: Array<{ label: string; priority: 'urgent' | 'reminder' | 'cancelled' | 'info' }> = [];

    if (lowerSubject.includes('urgent')) tags.push({ label: 'Urgent', priority: 'urgent' });
    if (lowerSubject.includes('reminder')) tags.push({ label: 'Reminder', priority: 'reminder' });
    if (lowerSubject.includes('cancelled')) tags.push({ label: 'Cancelled', priority: 'cancelled' });

    return tags;
  };

  return (
    <EmailListContainer>
      <StyledEmailList>
        {messages.map((message, index) => {
          // Simple logic: assume first 2 messages are unread for demo purposes
          const isUnread = index < 2;

          return (
            <EmailItem
              key={message.id}
              selected={selectedMessageId === message.id}
              unread={isUnread}
              divider={index < messages.length - 1}
            >
              <EmailItemButton
                selected={selectedMessageId === message.id}
                onClick={() => onSelectMessage(message.id)}
              >
                <EmailIconContainer>
                  <EmailIcon>{getCategoryIcon(message.subject)}</EmailIcon>
                </EmailIconContainer>
                <EmailContent
                  primary={
                    <EmailHeader>
                      <EmailSubject variant="subtitle2" unread={isUnread}>
                        {message.subject}
                      </EmailSubject>
                      <EmailMeta>
                        <EmailFrom>
                          {message.from}
                        </EmailFrom>
                        <EmailDate>
                          {message.date}
                        </EmailDate>
                      </EmailMeta>
                    </EmailHeader>
                  }
                  secondary={
                    <>
                      <EmailSnippet>
                        {message.snippet}
                      </EmailSnippet>
                      <EmailTagsContainer>
                        {getTags(message.subject).map((tag, tagIndex) => (
                          <EmailTag
                            key={tagIndex}
                            label={tag.label}
                            size="small"
                            priority={tag.priority}
                            variant="outlined"
                          />
                        ))}
                      </EmailTagsContainer>
                    </>
                  }
                />
              </EmailItemButton>
            </EmailItem>
          );
        })}
      </StyledEmailList>
    </EmailListContainer>
  );
};

export default EmailList;