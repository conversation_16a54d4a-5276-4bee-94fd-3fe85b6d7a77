// =============================================================================
// STYLED COMPONENTS LIBRARY
// =============================================================================

// Re-export from new modular structure
export * from '../../styles';

// Re-export common styled components (using new modular imports)
export {
  StyledContainer,
  FlexContainer,
  GridContainer,
  Section,
  PageHeader,
  EmptyState,
} from '../../styles/components/layout';

export {
  StyledCard,
  StatCard,
  StatCardContent,
} from '../../styles/components/cards';

export {
  StyledTypography,
  IconTypography,
} from '../../styles/components/typography';

// Re-export utility functions (using new modular imports)
export {
  getPriorityColor,
  createResponsiveSpacing,
  createHoverEffect,
  createFocusRing,
} from '../../styles/utils';

// Re-export theme extensions
export {
  themeExtensions,
  getPriorityColor as getThemePriorityColor,
  getStatusColor,
  createResponsiveValue,
  createHoverState,
  createFocusState,
  createDisabledState,
  createLoadingState,
  createTruncatedText,
  createGlassMorphism,
  createGradientBackground,
  cardVariants,
  buttonVariants,
} from '../../styles/themeExtensions';

// Re-export dashboard-specific components
export {
  DashboardContainer,
  DashboardContent,
  TabContent,
  DashboardAppBar,
  DashboardToolbar,
  UserSection,
  UserAvatar,
  StatsGrid,
  StatIcon,
  ActivityCard,
  ActivityHeader,
  ActivityTitle,
  ActivityMeta,
  ActivityDescription,
  PriorityChip,
  ReminderList,
  ReminderItem,
  MessageDetail,
  MessageBody,
  EmptyStateContainer,
  EmptyStateIcon,
  EmptyStateTitle,
  EmptyStateDescription,
} from './DashboardComponents';

// Email Components
export {
  EmailListContainer,
  EmailList,
  EmailItem,
  EmailItemButton,
  EmailIconContainer,
  EmailIcon,
  EmailContent,
  EmailHeader,
  EmailSubject,
  EmailMeta,
  EmailFrom,
  EmailDate,
  EmailSnippet,
  EmailTagsContainer,
  EmailTag,
  EmailEmptyStateContainer,
  EmailEmptyStateIcon,
  EmailEmptyStateTitle,
  EmailEmptyStateDescription,
} from './EmailComponents';

// Login Components
export {
  LoginPageContainer,
  LoginContainer,
  LoginCard,
  LoginCardContent,
  LoginHeader,
  LoginLogo,
  LoginTitle,
  LoginSubtitle,
  LoginDescription,
  LoginForm,
  LoginTextField,
  LoginSubmitButton,
  LoginAlert,
  LoginFooter,
  LoginToggleLink,
  LoginBackButton,
  LoginLoadingOverlay,
} from './LoginComponents';

// Layout Components (from LayoutComponents.ts - additional components not in styledUtils)
export {
  PageContainer,
  ContentContainer,
  HeroSection,
  CardGrid,
  SidebarLayout,
  Sidebar,
  MainContent,
  CenteredContent,
  Spacer,
  TabContainer,
  PaddedContent,
  StatsGridContainer,
  CenteredCardContent,
  LargeIconTypography,
  ExtraLargeIconTypography,
  SpacedCard,
  ResponsiveGridContainer,
  CenteredAvatar,
  SpacedTypography,
  MessageBodyContainer,
  PreLineTypography,
} from './LayoutComponents';

// UI Components
export {
  PrimaryButton,
  SecondaryButton,
  DangerButton,
  GhostButton,
  FeatureCard,
  InfoCard,
  WarningCard,
  SuccessCard,
  ErrorCard,
  FeatureCardContent,
  FeatureIcon,
  HeroTitle,
  HeroSubtitle,
  SectionTitle,
  FeatureTitle,
  FeatureDescription,
  StatsContainer,
  StatNumber,
  StatLabel,
  LogoContainer,
  ActionButtonGroup,
  PriorityBadge,
  StatusIndicator,
} from './UIComponents';

// App Page Components
export {
  MainLayoutContainer,
  StyledAppBar,
  AppBarActionsContainer,
  MenuButton,
  AppTitleTypography,
  LogoImage,
  NavigationTitle,
  BreadcrumbsContainer,
  BreadcrumbLink,
  NavigationAvatar,
} from './AppPageComponents';
