import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Avatar,
  Breadcrumbs
} from '@mui/material';
import { AccountCircle, Home, Dashboard as DashboardIcon, Login } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { User } from '../types';
import {
  LogoImage,
  NavigationTitle,
  BreadcrumbsContainer,
  BreadcrumbLink,
  NavigationAvatar,
} from './styled';

interface NavigationProps {
  user: User | null;
  onLogout?: () => void;
  showBreadcrumbs?: boolean;
}

const Navigation: React.FC<NavigationProps> = ({ user, onLogout, showBreadcrumbs = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    }
    navigate('/');
  };

  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [
      { label: t('nav.home'), path: '/', icon: <Home fontSize="small" /> }
    ];

    if (pathSegments.includes('dashboard')) {
      breadcrumbs.push({ 
        label: t('nav.dashboard'), 
        path: '/dashboard', 
        icon: <DashboardIcon fontSize="small" /> 
      });
    }

    if (pathSegments.includes('login')) {
      breadcrumbs.push({ 
        label: t('nav.login'), 
        path: '/login', 
        icon: <Login fontSize="small" /> 
      });
    }

    return breadcrumbs;
  };

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <LogoImage
            src="/Pigeon Squad Logo.png"
            alt="Pigeon Squad"
            onClick={() => handleNavigation('/')}
          />
          <NavigationTitle
            variant="h6"
            onClick={() => handleNavigation('/')}
          >
            {t('app.title')}
          </NavigationTitle>

          <Box display="flex" alignItems="center" gap={2}>
            {user ? (
              <>
                <Typography variant="body2">
                  {t('app.welcome', { name: user.name || user.email })}
                </Typography>
                <NavigationAvatar>
                  <Avatar>
                    <AccountCircle />
                  </Avatar>
                </NavigationAvatar>
                <Button color="inherit" onClick={handleLogout}>
                  {t('auth.logout')}
                </Button>
              </>
            ) : (
              <Button color="inherit" onClick={() => handleNavigation('/login')}>
                {t('auth.signIn')}
              </Button>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      {showBreadcrumbs && (
        <BreadcrumbsContainer>
          <Breadcrumbs aria-label="breadcrumb">
            {getBreadcrumbs().map((crumb) => (
              <BreadcrumbLink
                key={crumb.path}
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handleNavigation(crumb.path);
                }}
              >
                {crumb.icon}
                {crumb.label}
              </BreadcrumbLink>
            ))}
          </Breadcrumbs>
        </BreadcrumbsContainer>
      )}
    </>
  );
};

export default Navigation;
