import {
  Home,
  Dashboard as DashboardIcon,
  Analytics,
  ShoppingCart,
  Chat,
  CalendarToday,
  Apps,
  AccountCircle,
  ExitToApp,
  Settings,
  Link as LinkIcon,
  Email,
  Assessment,
} from '@mui/icons-material';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  path?: string;
  badge?: string | number;
  isNew?: boolean;
  children?: NavigationItem[];
  divider?: boolean;
}

export interface NavigationSection {
  id: string;
  title: string;
  items: NavigationItem[];
}

export const navigationConfig: NavigationSection[] = [
  {
    id: 'home',
    title: 'HOME',
    items: [
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: Home,
        path: '/dashboard',
        isNew: true,
      },
      {
        id: 'modern',
        label: 'Modern',
        icon: DashboardIcon,
        path: '/modern',
        isNew: true,
      },
      {
        id: 'analytical',
        label: 'Analytical',
        icon: Analytics,
        path: '/analytical',
      },
      {
        id: 'ecommerce',
        label: 'eCommerce',
        icon: ShoppingCart,
        path: '/ecommerce',
      },
    ],
  },
  {
    id: 'apps',
    title: 'APPS',
    items: [
      {
        id: 'chat',
        label: 'Chat',
        icon: Chat,
        path: '/chat',
      },
      {
        id: 'calendar',
        label: 'Calendar',
        icon: CalendarToday,
        path: '/calendar',
      },
      {
        id: 'email',
        label: 'Email',
        icon: Email,
        path: '/email',
      },
    ],
  },
  {
    id: 'other',
    title: 'OTHER',
    items: [
      {
        id: 'menu-level',
        label: 'Menu Level',
        icon: Apps,
        children: [
          {
            id: 'level-1',
            label: 'Level 1',
            icon: Apps,
            path: '/level-1',
          },
          {
            id: 'level-2',
            label: 'Level 2',
            icon: Apps,
            children: [
              {
                id: 'level-2-1',
                label: 'Level 2.1',
                icon: Apps,
                path: '/level-2-1',
              },
            ],
          },
        ],
      },
      {
        id: 'sample-page',
        label: 'Sample Page',
        icon: AccountCircle,
        path: '/sample',
      },
      {
        id: 'chip',
        label: 'Chip',
        icon: Settings,
        path: '/chip',
        badge: 6,
      },
      {
        id: 'outline',
        label: 'Outline',
        icon: Assessment,
        path: '/outline',
        badge: 'outlined',
      },
      {
        id: 'external-link',
        label: 'External Link',
        icon: LinkIcon,
        path: 'https://example.com',
      },
    ],
  },
];

// User profile navigation items
export const userProfileItems: NavigationItem[] = [
  {
    id: 'profile',
    label: 'Profile',
    icon: AccountCircle,
    path: '/profile',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    path: '/settings',
  },
  {
    id: 'logout',
    label: 'Logout',
    icon: ExitToApp,
    path: '/logout',
  },
];

// Helper function to get all navigation paths
export const getAllNavigationPaths = (): string[] => {
  const paths: string[] = [];
  
  const extractPaths = (items: NavigationItem[]) => {
    items.forEach(item => {
      if (item.path && !item.path.startsWith('http')) {
        paths.push(item.path);
      }
      if (item.children) {
        extractPaths(item.children);
      }
    });
  };

  navigationConfig.forEach(section => {
    extractPaths(section.items);
  });

  return paths;
};

// Helper function to find navigation item by path
export const findNavigationItemByPath = (path: string): NavigationItem | null => {
  const findInItems = (items: NavigationItem[]): NavigationItem | null => {
    for (const item of items) {
      if (item.path === path) {
        return item;
      }
      if (item.children) {
        const found = findInItems(item.children);
        if (found) return found;
      }
    }
    return null;
  };

  for (const section of navigationConfig) {
    const found = findInItems(section.items);
    if (found) return found;
  }

  return null;
};
