import {
  Home,
  AccountCircle,
  ExitToApp,
  Settings,
} from '@mui/icons-material';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  path?: string;
  badge?: string | number;
  isNew?: boolean;
  children?: NavigationItem[];
  divider?: boolean;
}

// Simplified navigation config - just a list of items without sections
export const navigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    path: '/dashboard',
  },
];

// User profile navigation items
export const userProfileItems: NavigationItem[] = [
  {
    id: 'profile',
    label: 'Profile',
    icon: AccountCircle,
    path: '/profile',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    path: '/settings',
  },
  {
    id: 'logout',
    label: 'Logout',
    icon: ExitToApp,
    path: '/logout',
  },
];

// Helper function to get all navigation paths
export const getAllNavigationPaths = (): string[] => {
  const paths: string[] = [];

  const extractPaths = (items: NavigationItem[]) => {
    items.forEach(item => {
      if (item.path && !item.path.startsWith('http')) {
        paths.push(item.path);
      }
      if (item.children) {
        extractPaths(item.children);
      }
    });
  };

  extractPaths(navigationConfig);

  return paths;
};

// Helper function to find navigation item by path
export const findNavigationItemByPath = (path: string): NavigationItem | null => {
  const findInItems = (items: NavigationItem[]): NavigationItem | null => {
    for (const item of items) {
      if (item.path === path) {
        return item;
      }
      if (item.children) {
        const found = findInItems(item.children);
        if (found) return found;
      }
    }
    return null;
  };

  return findInItems(navigationConfig);
};
