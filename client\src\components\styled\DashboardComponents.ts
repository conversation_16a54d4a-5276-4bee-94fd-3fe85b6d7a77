import { styled } from '@mui/material/styles';
import { 
  <PERSON>, 
  Card, 
  CardContent, 
  Typography, 
  AppBar, 
  Toolbar,
  Chip,
  Avatar,
  List,
  ListItem
} from '@mui/material';
import {
  createHoverEffect,
  createTruncatedText
} from '../../styles/themeExtensions';

// =============================================================================
// DASHBOARD LAYOUT COMPONENTS
// =============================================================================

/**
 * Main dashboard container
 */
export const DashboardContainer = styled(Box)(() => ({
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
}));

/**
 * Dashboard content area
 */
export const DashboardContent = styled(Box)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.palette.background.default,
}));

/**
 * Tab content container with consistent padding
 */
export const TabContent = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(4),
  },
}));

// =============================================================================
// HEADER COMPONENTS
// =============================================================================

/**
 * Enhanced app bar with custom styling
 */
export const DashboardAppBar = styled(AppBar)(({ theme }) => ({
  boxShadow: theme.custom.shadows.card,
  backgroundColor: theme.palette.primary.main,
  position: 'static',
}));

/**
 * Enhanced toolbar with proper spacing
 */
export const DashboardToolbar = styled(Toolbar)(({ theme }) => ({
  gap: theme.spacing(2),
  minHeight: 64,
  [theme.breakpoints.up('sm')]: {
    minHeight: 70,
  },
}));

/**
 * Logo container with hover effect
 */
export const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  ...createHoverEffect(theme, 'subtle'),
  cursor: 'pointer',
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(0.5),
}));

/**
 * User info section in header
 */
export const UserSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  marginLeft: 'auto',
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(1),
  },
}));

/**
 * Enhanced avatar with hover effect
 */
export const UserAvatar = styled(Avatar)(({ theme }) => ({
  width: 32,
  height: 32,
  transition: theme.transitions.create(['transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.1)',
  },
}));

// =============================================================================
// STATS COMPONENTS
// =============================================================================

/**
 * Stats grid container
 */
export const StatsGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
  gap: theme.spacing(3),
  marginBottom: theme.spacing(4),
  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: theme.spacing(2),
  },
}));

/**
 * Individual stat card with hover effects
 */
export const StatCard = styled(Card)(({ theme }) => ({
  height: '100%',
  ...createHoverEffect(theme, 'medium'),
  cursor: 'pointer',
  borderRadius: theme.spacing(2),
}));

/**
 * Stat card content with centered layout
 */
export const StatCardContent = styled(CardContent)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(1),
  '&:last-child': {
    paddingBottom: theme.spacing(3),
  },
}));

/**
 * Stat icon with consistent sizing
 */
export const StatIcon = styled(Typography)(({ theme }) => ({
  fontSize: '2.5rem',
  lineHeight: 1,
  marginBottom: theme.spacing(1),
  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
}));

/**
 * Stat number with emphasis
 */
export const StatNumber = styled(Typography)(({ theme }) => ({
  fontSize: '2rem',
  fontWeight: 700,
  lineHeight: 1,
  color: theme.palette.primary.main,
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.75rem',
  },
}));

/**
 * Stat label with secondary color
 */
export const StatLabel = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  fontWeight: 500,
  color: theme.palette.text.secondary,
  textAlign: 'center',
  ...createTruncatedText(2),
}));

// =============================================================================
// ACTIVITY COMPONENTS
// =============================================================================

/**
 * Activity card with priority border
 */
export const ActivityCard = styled(Card, {
  shouldForwardProp: (prop) => prop !== 'priority',
})<{ priority?: 'low' | 'medium' | 'high' }>(({ theme, priority }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(1.5),
  ...createHoverEffect(theme, 'subtle'),
  ...(priority && {
    borderLeft: `4px solid ${
      priority === 'high' 
        ? theme.palette.error.main
        : priority === 'medium'
        ? theme.palette.warning.main
        : theme.palette.success.main
    }`,
  }),
}));

/**
 * Activity header with flex layout
 */
export const ActivityHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  marginBottom: theme.spacing(1),
  gap: theme.spacing(2),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: theme.spacing(1),
  },
}));

/**
 * Activity title with proper typography
 */
export const ActivityTitle = styled(Typography)(() => ({
  fontSize: '1.125rem',
  fontWeight: 600,
  lineHeight: 1.4,
  flex: 1,
  ...createTruncatedText(2),
}));

/**
 * Activity meta information
 */
export const ActivityMeta = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(1),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  flexWrap: 'wrap',
}));

/**
 * Activity description with line clamping
 */
export const ActivityDescription = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  lineHeight: 1.5,
  color: theme.palette.text.primary,
  ...createTruncatedText(3),
}));

/**
 * Priority chip with custom styling
 */
export const PriorityChip = styled(Chip, {
  shouldForwardProp: (prop) => prop !== 'priority',
})<{ priority?: 'low' | 'medium' | 'high' }>(({ theme, priority }) => ({
  fontSize: '0.75rem',
  fontWeight: 600,
  height: 24,
  borderRadius: theme.spacing(1),
  textTransform: 'uppercase',
  letterSpacing: '0.025em',
  ...(priority === 'high' && {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
  }),
  ...(priority === 'medium' && {
    backgroundColor: theme.palette.warning.main,
    color: theme.palette.warning.contrastText,
  }),
  ...(priority === 'low' && {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText,
  }),
}));

// =============================================================================
// REMINDER COMPONENTS
// =============================================================================

/**
 * Enhanced list for reminders
 */
export const ReminderList = styled(List)(({ theme }) => ({
  padding: 0,
  '& .MuiListItem-root': {
    borderRadius: theme.spacing(1),
    marginBottom: theme.spacing(1),
    transition: theme.transitions.create(['background-color'], {
      duration: theme.transitions.duration.short,
    }),
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
    '&:last-child': {
      marginBottom: 0,
    },
  },
}));

/**
 * Reminder item with enhanced styling
 */
export const ReminderItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  border: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(1),
  '&:last-child': {
    marginBottom: 0,
  },
}));

// =============================================================================
// MESSAGE COMPONENTS
// =============================================================================

/**
 * Message detail container
 */
export const MessageDetail = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.spacing(1),
  border: `1px solid ${theme.palette.divider}`,
  marginTop: theme.spacing(2),
}));

/**
 * Message body with proper formatting
 */
export const MessageBody = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  lineHeight: 1.6,
  whiteSpace: 'pre-line',
  color: theme.palette.text.primary,
}));

// =============================================================================
// EMPTY STATE COMPONENTS
// =============================================================================

/**
 * Empty state container for tabs
 */
export const EmptyStateContainer = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(6, 3),
  color: theme.palette.text.secondary,
}));

/**
 * Empty state icon
 */
export const EmptyStateIcon = styled(Typography)(({ theme }) => ({
  fontSize: '4rem',
  lineHeight: 1,
  marginBottom: theme.spacing(2),
  opacity: 0.6,
}));

/**
 * Empty state title
 */
export const EmptyStateTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(1),
  color: theme.palette.text.primary,
}));

/**
 * Empty state description
 */
export const EmptyStateDescription = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  lineHeight: 1.5,
  color: theme.palette.text.secondary,
  maxWidth: 400,
  margin: '0 auto',
}));
