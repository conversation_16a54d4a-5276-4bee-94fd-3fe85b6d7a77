import "reflect-metadata"
import { DataSource } from "typeorm"
import { User } from "./entities/User"
import { EmailLog } from "./entities/EmailLog"

export const AppDataSource = new DataSource({
    type: "sqlite",
    database: "./database.db",
    synchronize: false,
    logging: false,
    entities: [User, EmailLog],
    migrations: [process.env.NODE_ENV === "production" ? "dist/migrations/*.js" : "migrations/*.ts"],
    subscribers: [],
})