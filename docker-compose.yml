version: '3.8'

services:
  server:
    container_name: pigeonsquad-server
    image: pigeonsquad-server
    build: ./server
    ports:
      - "4200:4200"
    env_file:
      - ../.serverenv
    volumes:
      - ../database.db:/app/database.db

  client:
    container_name: pigeonsquad-client
    image: pigeonsquad-client
    build: ./client
    ports:
      - "4173:4173"
    env_file:
      - ../.clientenv      
    depends_on:
      - server
