import express, { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import AppDataSource from '../db';
import { User } from '../entities/User';

const router = express.Router();

interface AuthRequest {
  email: string;
  password: string;
}

interface RegisterRequest extends AuthRequest {
  name: string;
}

router.post('/register', async (req: Request<{}, {}, RegisterRequest>, res: Response) => {
  try {
    const { email, password, name } = req.body;
    console.log(`\x1b[36m👤 Registration attempt for email: ${email}\x1b[0m`);

    const userRepository = AppDataSource.getRepository(User);
    const existingUser = await userRepository.findOne({ where: { email } });
    if (existingUser) {
      console.log(`\x1b[33m⚠️  Registration failed - User already exists: ${email}\x1b[0m`);
      return res.status(400).json({ error: 'User already exists' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user = userRepository.create({ email, password: hashedPassword, name });
    await userRepository.save(user);
    console.log(`\x1b[32m✅ User registered successfully - ID: ${user.id}, Email: ${email}\x1b[0m`);

    const token = jwt.sign({ id: user.id, email: user.email }, process.env.JWT_SECRET || 'fallback-secret');
    console.log(`\x1b[32m🔑 JWT token generated for user: ${user.id}\x1b[0m`);
    res.json({ token, user: { id: user.id, email: user.email, name: user.name } });
  } catch (error) {
    console.error('\x1b[31m❌ Registration error:\x1b[0m', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

router.post('/login', async (req: Request<{}, {}, AuthRequest>, res: Response) => {
  try {
    const { email, password } = req.body;
    console.log(`\x1b[36m🔐 Login attempt for email: ${email}\x1b[0m`);

    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({ where: { email } });

    if (!user || !await bcrypt.compare(password, user.password)) {
      console.log(`\x1b[31m🚫 Login failed - Invalid credentials for: ${email}\x1b[0m`);
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    console.log(`\x1b[32m✅ Login successful for user: ${user.id} (${email})\x1b[0m`);
    const token = jwt.sign({ id: user.id, email: user.email }, process.env.JWT_SECRET || 'fallback-secret');
    console.log(`\x1b[32m🔑 JWT token generated for user: ${user.id}\x1b[0m`);
    res.json({ token, user: { id: user.id, email: user.email, name: user.name } });
  } catch (error) {
    console.error('\x1b[31m❌ Login error:\x1b[0m', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

export default router;