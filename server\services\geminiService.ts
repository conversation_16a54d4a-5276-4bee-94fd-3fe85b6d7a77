import { GoogleGenerativeAI } from '@google/generative-ai';
import { ParsedMail } from 'mailparser';
import { AppDataSource } from '../data-source';
import { User } from '../entities/User';
import { EmailLog } from '../entities/EmailLog';

interface QueuedEmail {
  email: ParsedMail;
  retryCount: number;
  nextRetry: number;
}

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private retryQueue: QueuedEmail[] = [];
  private maxRetries = 5;
  private baseDelay = 5000; // 5 seconds
  private processingEmails = new Set<string>(); // Track emails being processed

  // Configurable prompt for family activity monitoring
  private readonly FAMILY_ACTIVITY_PROMPT = `
You are an AI assistant for a family activity monitoring system called "Pigeon Squad". 
Your job is to analyze incoming emails and extract relevant family activity information.

Please analyze the following email and identify:
1. Any family activities mentioned (sports, school events, appointments, etc.)
2. Important dates and times
3. Family member names mentioned
4. Location information
5. Any action items or reminders
6. Overall sentiment/urgency level

Respond in JSON format with the following structure:
{
  "activities": ["list of activities found"],
  "dates": ["list of dates/times mentioned"],
  "family_members": ["list of family member names"],
  "locations": ["list of locations mentioned"],
  "action_items": ["list of things that need to be done"],
  "urgency": "low|medium|high",
  "summary": "brief summary of the email content"
}

Email to analyze:
`;

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }

    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
    this.startRetryProcessor();
  }

  async analyzeEmail(parsedEmail: ParsedMail): Promise<void> {
    const emailKey = this.getEmailKey(parsedEmail);
    
    // Check if already processing this email
    if (this.processingEmails.has(emailKey)) {
      console.log('\x1b[33m⏭️  Email already being processed, skipping...\x1b[0m');
      return;
    }
    
    // Check if already exists in database
    if (await this.emailAlreadyProcessed(parsedEmail)) {
      console.log('\x1b[33m✅ Email already processed, skipping...\x1b[0m');
      return;
    }
    
    this.processingEmails.add(emailKey);
    
    try {
      const emailContent = this.formatEmailForAnalysis(parsedEmail);
      const prompt = this.FAMILY_ACTIVITY_PROMPT + emailContent;

      console.log('\x1b[35m🤖 Analyzing email with Gemini AI...\x1b[0m');
      
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const analysisResult = response.text();

      console.log('\x1b[42m\x1b[30m🧠 === GEMINI AI ANALYSIS ===\x1b[0m');
      console.log(analysisResult);
      console.log('\x1b[42m\x1b[30m============================\x1b[0m');

      // Store the analysis result in the database
      await this.storeEmailAnalysis(parsedEmail, analysisResult);

    } catch (error: any) {
      if (error.status === 503 || error.message?.includes('overloaded')) {
        console.log('\x1b[33m⏳ Model overloaded, adding to retry queue...\x1b[0m');
        this.addToRetryQueue(parsedEmail);
      } else {
        console.error('\x1b[31m❌ Error analyzing email with Gemini:\x1b[0m', error);
      }
    } finally {
      this.processingEmails.delete(emailKey);
    }
  }

  private formatEmailForAnalysis(parsedEmail: ParsedMail): string {
    return `
From: ${parsedEmail.from?.text || 'Unknown'}
To: ${Array.isArray(parsedEmail.to) ? parsedEmail.to.map(t => t.text).join(', ') : parsedEmail.to?.text || 'Unknown'}
Subject: ${parsedEmail.subject || 'No subject'}
Date: ${parsedEmail.date}
Body: ${parsedEmail.text || parsedEmail.html || 'No content'}
    `.trim();
  }

  private getEmailKey(parsedEmail: ParsedMail): string {
    return `${parsedEmail.messageId || parsedEmail.subject}-${parsedEmail.date}`;
  }

  private async emailAlreadyProcessed(parsedEmail: ParsedMail): Promise<boolean> {
    try {
      const emailLogRepository = AppDataSource.getRepository(EmailLog);
      const existing = await emailLogRepository.findOne({
        where: {
          subject: parsedEmail.subject || 'No subject',
          fromEmail: parsedEmail.from?.value?.[0]?.address || parsedEmail.from?.text || ''
        }
      });
      return !!existing;
    } catch (error) {
      console.error('\x1b[31m❌ Error checking existing email:\x1b[0m', error);
      return false;
    }
  }

  private addToRetryQueue(email: ParsedMail): void {
    const emailKey = this.getEmailKey(email);
    
    // Don't add to retry queue if already processing or already queued
    if (this.processingEmails.has(emailKey)) {
      return;
    }
    
    // Check if already in retry queue
    const alreadyQueued = this.retryQueue.some(item => 
      this.getEmailKey(item.email) === emailKey
    );
    
    if (alreadyQueued) {
      console.log('\x1b[33m⏭️  Email already in retry queue, skipping...\x1b[0m');
      return;
    }
    
    this.retryQueue.push({
      email,
      retryCount: 0,
      nextRetry: Date.now() + this.baseDelay
    });
  }

  private startRetryProcessor(): void {
    setInterval(() => {
      this.processRetryQueue();
    }, 10000); // Check every 10 seconds
  }

  private async storeEmailAnalysis(parsedEmail: ParsedMail, analysisResult: string): Promise<void> {
    try {
      const fromEmail = parsedEmail.from?.value?.[0]?.address || parsedEmail.from?.text || '';
      
      // Find user by email
      const userRepository = AppDataSource.getRepository(User);
      const user = await userRepository.findOne({ where: { email: fromEmail } });
      
      if (!user) {
        console.log(`\x1b[33m⚠️  No user found for email: ${fromEmail}, skipping storage\x1b[0m`);
        return;
      }

      // Parse the JSON response to extract summary
      let summary = 'AI analysis completed';
      try {
        // Clean the response by removing any markdown code blocks or extra whitespace
        const cleanedResult = analysisResult.replace(/```json\n?|```\n?/g, '').trim();
        const parsed = JSON.parse(cleanedResult);
        summary = parsed.summary || summary;
      } catch (e) {
        console.log('\x1b[33m⚠️  Could not parse JSON response for summary:\x1b[0m', e instanceof Error ? e.message : String(e));
        console.log('\x1b[33m📄 Raw response:\x1b[0m', analysisResult.substring(0, 200) + '...');
      }

      // Store in database
      const emailLogRepository = AppDataSource.getRepository(EmailLog);
      const emailLog = new EmailLog();
      emailLog.subject = parsedEmail.subject || 'No subject';
      emailLog.summary = summary;
      emailLog.fullJsonResponse = analysisResult;
      emailLog.fromEmail = fromEmail;
      emailLog.user = user;
      emailLog.userId = user.id;

      await emailLogRepository.save(emailLog);
      console.log(`\x1b[32m✅ Stored email analysis for user: ${user.email}\x1b[0m`);
      
    } catch (error) {
      console.error('\x1b[31m❌ Error storing email analysis:\x1b[0m', error);
    }
  }

  private async processRetryQueue(): Promise<void> {
    const now = Date.now();
    const readyToRetry = this.retryQueue.filter(item => item.nextRetry <= now);
    
    for (const item of readyToRetry) {
      const emailKey = this.getEmailKey(item.email);
      
      // Skip if already processing or already exists
      if (this.processingEmails.has(emailKey) || await this.emailAlreadyProcessed(item.email)) {
        this.retryQueue = this.retryQueue.filter(q => q !== item);
        continue;
      }
      
      this.processingEmails.add(emailKey);
      
      try {
        const emailContent = this.formatEmailForAnalysis(item.email);
        const prompt = this.FAMILY_ACTIVITY_PROMPT + emailContent;

        console.log(`\x1b[35m🔄 Retrying email analysis (attempt ${item.retryCount + 1})...\x1b[0m`);
        
        const result = await this.model.generateContent(prompt);
        const response = await result.response;
        const analysisResult = response.text();

        console.log('\x1b[42m\x1b[30m🧠 === GEMINI AI ANALYSIS (RETRY) ===\x1b[0m');
        console.log(analysisResult);
        console.log('\x1b[42m\x1b[30m==================================\x1b[0m');

        // Store the analysis result in the database
        await this.storeEmailAnalysis(item.email, analysisResult);

        // Remove from queue on success
        this.retryQueue = this.retryQueue.filter(q => q !== item);
        
      } catch (error: any) {
        item.retryCount++;
        
        if (item.retryCount >= this.maxRetries) {
          console.error(`\x1b[31m❌ Max retries reached for email, removing from queue\x1b[0m`);
          this.retryQueue = this.retryQueue.filter(q => q !== item);
        } else if (error.status === 503 || error.message?.includes('overloaded')) {
          // Exponential backoff
          item.nextRetry = now + (this.baseDelay * Math.pow(2, item.retryCount));
          console.log(`\x1b[33m⏳ Still overloaded, will retry in ${Math.round((item.nextRetry - now) / 1000)}s\x1b[0m`);
        } else {
          console.error('\x1b[31m❌ Non-recoverable error, removing from queue:\x1b[0m', error);
          this.retryQueue = this.retryQueue.filter(q => q !== item);
        }
      } finally {
        this.processingEmails.delete(emailKey);
      }
    }
  }
}

export default GeminiService;