// =============================================================================
// STYLED COMPONENT PROP INTERFACES
// =============================================================================

/**
 * Props for components that accept spacing configuration
 */
export interface SpacingProps {
  spacing?: number | string;
  gap?: number | string;
}

/**
 * Props for components that accept color variants
 */
export interface VariantProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

/**
 * Props for components that accept priority levels
 */
export interface PriorityProps {
  priority?: 'low' | 'medium' | 'high';
}

/**
 * Props for components that accept size variants
 */
export interface SizeProps {
  size?: 'small' | 'medium' | 'large';
}
