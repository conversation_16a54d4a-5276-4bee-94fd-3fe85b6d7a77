import React from 'react';
import { Typography, Box, Card, CardContent } from '@mui/material';
import AppPage from '../../components/App/AppPage';
import { DashboardProps } from '../../types';

const ModernDashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {

  return (
    <AppPage user={user} onLogout={onLogout} title="Modern Dashboard">
      <Box>
        <Typography variant="h4" gutterBottom>
          Modern Dashboard
        </Typography>
        <Card>
          <CardContent>
            <Typography variant="body1">
              This is the modern dashboard page. Content will be added here.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </AppPage>
  );
};

export default ModernDashboard;
