import { styled } from '@mui/material/styles';
import {
  Card,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Typography,
  Box,
  Chip,
  CardContent,
} from '@mui/material';
import {
  createHoverEffect,
  extendedShadows,
  animations,
} from '../../styles/themeExtensions';

// Email List Container
export const EmailListContainer = styled(Card)(({ theme }) => ({
  overflow: 'hidden',
  ...createHoverEffect(theme, 'subtle'),
}));

// Email List
export const EmailList = styled(List)(({ theme }) => ({
  padding: 0,
  '& .MuiListItem-divider': {
    borderColor: theme.palette.divider,
  },
}));

// Email Item with selection and hover states
interface EmailItemProps {
  selected?: boolean;
  unread?: boolean;
}

export const EmailItem = styled(ListItem)<EmailItemProps>(({ theme, selected, unread }) => ({
  padding: 0,
  position: 'relative',
  ...(selected && {
    backgroundColor: theme.palette.action.selected,
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: 4,
      backgroundColor: theme.palette.primary.main,
    },
  }),
  ...(unread && {
    backgroundColor: theme.palette.action.hover,
    '&::after': {
      content: '""',
      position: 'absolute',
      right: 8,
      top: '50%',
      transform: 'translateY(-50%)',
      width: 8,
      height: 8,
      borderRadius: '50%',
      backgroundColor: theme.palette.primary.main,
    },
  }),
}));

// Email Item Button with enhanced hover effects
export const EmailItemButton = styled(ListItemButton)<EmailItemProps>(({ theme, selected }) => ({
  padding: theme.spacing(2),
  minHeight: 120,
  transition: theme.transitions.create(['background-color', 'transform'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    backgroundColor: selected 
      ? theme.palette.action.selected 
      : theme.palette.action.hover,
    transform: 'translateX(4px)',
  },
  '&.Mui-selected': {
    backgroundColor: theme.palette.action.selected,
    '&:hover': {
      backgroundColor: theme.palette.action.selected,
    },
  },
}));

// Email Icon Container
export const EmailIconContainer = styled(ListItemIcon)(({ theme }) => ({
  minWidth: 48,
  display: 'flex',
  alignItems: 'flex-start',
  paddingTop: theme.spacing(0.5),
}));

// Email Icon
export const EmailIcon = styled(Typography)(() => ({
  fontSize: '1.5rem',
  lineHeight: 1,
}));

// Email Content Container
export const EmailContent = styled(ListItemText)(({ theme }) => ({
  margin: 0,
  '& .MuiListItemText-primary': {
    marginBottom: theme.spacing(1),
  },
}));

// Email Header
export const EmailHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
}));

// Email Subject
interface EmailSubjectProps {
  unread?: boolean;
}

export const EmailSubject = styled(Typography)<EmailSubjectProps>(({ unread }) => ({
  fontWeight: unread ? 600 : 400,
  fontSize: '0.875rem',
  lineHeight: 1.2,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
}));

// Email Meta (from and date)
export const EmailMeta = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

// Email From
export const EmailFrom = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  maxWidth: '60%',
  flex: 1,
}));

// Email Date
export const EmailDate = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
  whiteSpace: 'nowrap',
  flexShrink: 0,
}));

// Email Body/Snippet
export const EmailSnippet = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  lineHeight: 1.3,
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  marginTop: theme.spacing(1),
  marginBottom: theme.spacing(1),
}));

// Email Tags Container
export const EmailTagsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(0.5),
  flexWrap: 'wrap',
  marginTop: theme.spacing(1),
}));

// Email Tag with priority-based styling
interface EmailTagProps {
  priority?: 'urgent' | 'reminder' | 'cancelled' | 'info';
}

export const EmailTag = styled(Chip)<EmailTagProps>(({ theme, priority }) => {
  const getTagColor = () => {
    switch (priority) {
      case 'urgent':
        return {
          backgroundColor: theme.palette.error.light,
          color: theme.palette.error.contrastText,
          borderColor: theme.palette.error.main,
        };
      case 'reminder':
        return {
          backgroundColor: theme.palette.info.light,
          color: theme.palette.info.contrastText,
          borderColor: theme.palette.info.main,
        };
      case 'cancelled':
        return {
          backgroundColor: theme.palette.warning.light,
          color: theme.palette.warning.contrastText,
          borderColor: theme.palette.warning.main,
        };
      default:
        return {
          backgroundColor: theme.palette.grey[100],
          color: theme.palette.text.secondary,
          borderColor: theme.palette.grey[300],
        };
    }
  };

  return {
    fontSize: '0.6875rem',
    height: 20,
    ...getTagColor(),
    transition: theme.transitions.create(['transform', 'box-shadow'], {
      duration: animations.duration.fast,
    }),
    '&:hover': {
      transform: 'scale(1.05)',
      boxShadow: extendedShadows.cardHover,
    },
  };
});

// Empty State Container for EmailList
export const EmailEmptyStateContainer = styled(CardContent)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

// Empty State Icon
export const EmailEmptyStateIcon = styled(Typography)(({ theme }) => ({
  fontSize: '3rem',
  lineHeight: 1,
  marginBottom: theme.spacing(1),
}));

// Empty State Title
export const EmailEmptyStateTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  marginBottom: theme.spacing(1),
}));

// Empty State Description
export const EmailEmptyStateDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  maxWidth: 300,
  lineHeight: 1.5,
}));
