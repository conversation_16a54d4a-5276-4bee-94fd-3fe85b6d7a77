import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, Avatar, IconButton, Button } from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';
import { User } from '../../../types';

// Styled Components
const ProfileSectionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const MobileMenuButton = styled(IconButton)(({ theme }) => ({
  color: 'white',
  display: 'none',
  [theme.breakpoints.down('md')]: {
    display: 'flex',
  },
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 36,
  height: 36,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  cursor: 'pointer',
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 4px 12px rgba(255, 255, 255, 0.3)',
  },
}));

const AuthButtonsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  [theme.breakpoints.down('md')]: {
    display: 'none', // Hide auth buttons on mobile, show mobile menu instead
  },
}));

const AuthButton = styled(Button)(({ theme }) => ({
  color: 'white',
  borderColor: 'rgba(255, 255, 255, 0.3)',
  fontSize: '0.875rem',
  fontWeight: 500,
  padding: theme.spacing(0.5, 2),
  minWidth: 'auto',
  transition: theme.transitions.create(['background-color', 'border-color', 'transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.5)',
    transform: 'translateY(-1px)',
  },
}));

const LoginButton = styled(AuthButton)({
  variant: 'outlined',
});

const RegisterButton = styled(AuthButton)(() => ({
  backgroundColor: 'rgba(255, 255, 255, 0.15)',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
  },
}));

// Component Props Interface
interface ProfileSectionProps {
  user?: User | null;
  onProfileClick?: () => void;
  onMobileMenuClick?: () => void;
  onLoginClick?: () => void;
  onRegisterClick?: () => void;
  profileImageSrc?: string;
  profileAltText?: string;
}

const ProfileSection: React.FC<ProfileSectionProps> = ({
  user,
  onProfileClick,
  onMobileMenuClick,
  onLoginClick,
  onRegisterClick,
  profileImageSrc = "/Pigeon Squad Logo.png",
  profileAltText = "Profile"
}) => {
  return (
    <ProfileSectionContainer>
      <MobileMenuButton onClick={onMobileMenuClick}>
        <MenuIcon />
      </MobileMenuButton>

      {user ? (
        // Show profile avatar when user is authenticated
        <ProfileAvatar onClick={onProfileClick}>
          <img src={profileImageSrc} alt={profileAltText} />
        </ProfileAvatar>
      ) : (
        // Show login/register buttons when user is not authenticated
        <AuthButtonsContainer>
          <LoginButton
            variant="outlined"
            size="small"
            onClick={onLoginClick}
          >
            Login
          </LoginButton>
          <RegisterButton
            variant="contained"
            size="small"
            onClick={onRegisterClick}
          >
            Register
          </RegisterButton>
        </AuthButtonsContainer>
      )}
    </ProfileSectionContainer>
  );
};

export default ProfileSection;
