import { styled } from '@mui/material/styles';
import { <PERSON><PERSON>, <PERSON>, CardContent, Typography, Box } from '@mui/material';
import {
  extendedShadows,
  animations,
} from '../../styles/themeExtensions';

// Enhanced Button Variants
export const PrimaryButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1.5, 3),
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
  boxShadow: `0 4px 12px ${theme.palette.primary.main}40`,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
    transform: 'translateY(-2px)',
    boxShadow: `0 6px 20px ${theme.palette.primary.main}50`,
  },
  '&:active': {
    transform: 'translateY(0)',
  },
  '&.Mui-disabled': {
    background: theme.palette.action.disabledBackground,
    color: theme.palette.action.disabled,
    boxShadow: 'none',
    transform: 'none',
  },
}));

export const SecondaryButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1.5, 3),
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  borderColor: theme.palette.primary.main,
  color: theme.palette.primary.main,
  backgroundColor: 'transparent',
  transition: theme.transitions.create(['transform', 'background-color', 'box-shadow'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    backgroundColor: `${theme.palette.primary.main}10`,
    borderColor: theme.palette.primary.dark,
    transform: 'translateY(-1px)',
    boxShadow: `0 4px 12px ${theme.palette.primary.main}20`,
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

export const DangerButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1.5, 3),
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  backgroundColor: theme.palette.error.main,
  color: theme.palette.error.contrastText,
  boxShadow: `0 4px 12px ${theme.palette.error.main}40`,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    backgroundColor: theme.palette.error.dark,
    transform: 'translateY(-2px)',
    boxShadow: `0 6px 20px ${theme.palette.error.main}50`,
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

export const GhostButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1.5, 3),
  fontSize: '1rem',
  fontWeight: 600,
  textTransform: 'none',
  backgroundColor: 'transparent',
  color: theme.palette.text.secondary,
  border: 'none',
  transition: theme.transitions.create(['color', 'background-color', 'transform'], {
    duration: animations.duration.fast,
  }),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    color: theme.palette.text.primary,
    transform: 'translateY(-1px)',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

// Enhanced Card Variants
interface FeatureCardProps {
  interactive?: boolean;
  elevated?: boolean;
}

export const FeatureCard = styled(Card)<FeatureCardProps>(({ theme, interactive, elevated }) => ({
  height: '100%',
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: elevated ? extendedShadows.cardElevated : extendedShadows.card,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: animations.duration.fast,
  }),
  ...(interactive && {
    cursor: 'pointer',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: extendedShadows.cardHover,
    },
  }),
}));

export const InfoCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(1.5),
  backgroundColor: `${theme.palette.info.main}10`,
  border: `1px solid ${theme.palette.info.main}30`,
  '& .MuiCardContent-root': {
    padding: theme.spacing(3),
  },
}));

export const WarningCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(1.5),
  backgroundColor: `${theme.palette.warning.main}10`,
  border: `1px solid ${theme.palette.warning.main}30`,
  '& .MuiCardContent-root': {
    padding: theme.spacing(3),
  },
}));

export const SuccessCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(1.5),
  backgroundColor: `${theme.palette.success.main}10`,
  border: `1px solid ${theme.palette.success.main}30`,
  '& .MuiCardContent-root': {
    padding: theme.spacing(3),
  },
}));

export const ErrorCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(1.5),
  backgroundColor: `${theme.palette.error.main}10`,
  border: `1px solid ${theme.palette.error.main}30`,
  '& .MuiCardContent-root': {
    padding: theme.spacing(3),
  },
}));

// Enhanced Card Content
export const FeatureCardContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(3),
  textAlign: 'center',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
  height: '100%',
}));

// Icon Container for feature cards
export const FeatureIcon = styled(Box)(({ theme }) => ({
  fontSize: '3rem',
  lineHeight: 1,
  marginBottom: theme.spacing(1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 80,
  height: 80,
  borderRadius: '50%',
  backgroundColor: `${theme.palette.primary.main}10`,
  color: theme.palette.primary.main,
}));

// Typography Variants
export const HeroTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  textAlign: 'center',
  marginBottom: theme.spacing(2),
}));

export const HeroSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  color: theme.palette.text.secondary,
  textAlign: 'center',
  marginBottom: theme.spacing(3),
  maxWidth: 600,
  margin: '0 auto',
  lineHeight: 1.6,
}));

export const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  textAlign: 'center',
  marginBottom: theme.spacing(6),
  color: theme.palette.text.primary,
}));

export const FeatureTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  marginBottom: theme.spacing(1),
  color: theme.palette.text.primary,
}));

export const FeatureDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  lineHeight: 1.6,
  flex: 1,
}));

// Stats Components
export const StatsContainer = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(8, 0),
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
}));

export const StatNumber = styled(Typography)(({ theme }) => ({
  fontWeight: 'bold',
  fontSize: '3rem',
  lineHeight: 1,
  marginBottom: theme.spacing(1),
  [theme.breakpoints.down('sm')]: {
    fontSize: '2rem',
  },
}));

export const StatLabel = styled(Typography)(() => ({
  fontSize: '1rem',
  opacity: 0.9,
  fontWeight: 500,
}));

// Logo Container
export const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& img': {
    width: 100,
    height: 100,
    transition: theme.transitions.create(['transform'], {
      duration: animations.duration.normal,
    }),
    '&:hover': {
      transform: 'scale(1.05) rotate(5deg)',
    },
  },
}));

// Action Button Group
export const ActionButtonGroup = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(2),
  justifyContent: 'center',
  flexWrap: 'wrap',
  marginTop: theme.spacing(4),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'center',
    '& > *': {
      width: '100%',
      maxWidth: 300,
    },
  },
}));

// Priority Badge
interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high';
}

export const PriorityBadge = styled(Box)<PriorityBadgeProps>(({ theme, priority }) => {
  const getColor = () => {
    switch (priority) {
      case 'high':
        return {
          backgroundColor: theme.palette.error.light,
          color: theme.palette.error.contrastText,
          borderColor: theme.palette.error.main,
        };
      case 'medium':
        return {
          backgroundColor: theme.palette.warning.light,
          color: theme.palette.warning.contrastText,
          borderColor: theme.palette.warning.main,
        };
      default:
        return {
          backgroundColor: theme.palette.success.light,
          color: theme.palette.success.contrastText,
          borderColor: theme.palette.success.main,
        };
    }
  };

  return {
    display: 'inline-flex',
    alignItems: 'center',
    padding: theme.spacing(0.5, 1),
    borderRadius: theme.spacing(0.5),
    fontSize: '0.75rem',
    fontWeight: 600,
    textTransform: 'uppercase',
    border: '1px solid',
    ...getColor(),
  };
});

// Status Indicator
interface StatusIndicatorProps {
  status: 'active' | 'inactive' | 'pending' | 'completed';
  size?: 'small' | 'medium' | 'large';
}

export const StatusIndicator = styled(Box)<StatusIndicatorProps>(({ theme, status, size = 'medium' }) => {
  const getSize = () => {
    switch (size) {
      case 'small':
        return 8;
      case 'large':
        return 16;
      default:
        return 12;
    }
  };

  const getColor = () => {
    switch (status) {
      case 'active':
        return theme.palette.success.main;
      case 'inactive':
        return theme.palette.grey[400];
      case 'pending':
        return theme.palette.warning.main;
      case 'completed':
        return theme.palette.info.main;
      default:
        return theme.palette.grey[400];
    }
  };

  return {
    width: getSize(),
    height: getSize(),
    borderRadius: '50%',
    backgroundColor: getColor(),
    display: 'inline-block',
    flexShrink: 0,
  };
});
