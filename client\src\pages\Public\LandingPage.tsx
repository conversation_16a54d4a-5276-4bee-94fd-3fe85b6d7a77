import React from 'react';
import { Typography, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { LandingPageProps } from '../../types';
import NavBar from '../../components/Public/NavBar/NavBar';
import {
  PageContainer,
  Section,
  ContentContainer,
  HeroSection,
  CardGrid,
  CenteredContent,
  PrimaryButton,
  SecondaryButton,
  FeatureCard,
  FeatureCardContent,
  FeatureIcon,
  HeroTitle,
  HeroSubtitle,
  SectionTitle,
  FeatureTitle,
  FeatureDescription,
  StatsContainer,
  StatNumber,
  StatLabel,
  LogoContainer,
  ActionButtonGroup,
} from '../../components/styled';
import { styled } from '@mui/material/styles';

// Styled components for LandingPage
const PaddedHeroSection = styled(HeroSection)(() => ({
  paddingTop: '80px',
}));

const SpacedHeroTitle = styled(HeroTitle)(({ theme }) => ({
  marginTop: theme.spacing(4),
}));

const CenteredTypography = styled(Typography)(() => ({
  textAlign: 'center',
  maxWidth: 800,
  margin: '0 auto',
}));

const LandingPage: React.FC<LandingPageProps> = ({ user, onShowLogin, onShowRegister }) => {
  const { t } = useTranslation();
  
  const scrollToFeatures = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    const featuresSection = document.getElementById('features-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  const handleNavigation = (section: string) => {
    const element = document.getElementById(`${section}-section`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleProfileClick = () => {
    onShowLogin();
  };

  return (
    <PageContainer>
      <NavBar
        user={user}
        onNavigate={handleNavigation}
        onProfileClick={handleProfileClick}
        onLoginClick={onShowLogin}
        onRegisterClick={onShowRegister}
      />
      <PaddedHeroSection id="home-section" minHeight="100vh" background="gradient">
        <ContentContainer maxWidth="md">
          <CenteredContent maxWidth={800} padding={8}>
            <LogoContainer>
              <img src="/Pigeon Squad Logo.png" alt="Pigeon Squad" />
            </LogoContainer>
            <HeroTitle variant="h4">{t('app.title')}</HeroTitle>
            <Typography variant="h6" color="text.secondary" gutterBottom>{t('landing.subtitle')}</Typography>

            <SpacedHeroTitle variant="h4">
              {t('landing.headline')}
            </SpacedHeroTitle>
            <HeroSubtitle variant="body1">
              {t('landing.description')}
            </HeroSubtitle>

            <ActionButtonGroup>
              <PrimaryButton size="large" onClick={onShowLogin}>
                {t('landing.getStarted')}
              </PrimaryButton>
              <SecondaryButton variant="outlined" size="large" onClick={scrollToFeatures}>
                {t('landing.learnMore')}
              </SecondaryButton>
            </ActionButtonGroup>
          </CenteredContent>
        </ContentContainer>
      </PaddedHeroSection>

      <Section id="features-section" spacing="large" background="paper">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('landing.howWeHelp')}
          </SectionTitle>
          <CardGrid container spacing={4}>
            {[
              { icon: '📧', titleKey: 'landing.features.smartEmail.title', descKey: 'landing.features.smartEmail.desc' },
              { icon: '📅', titleKey: 'landing.features.autoReminders.title', descKey: 'landing.features.autoReminders.desc' },
              { icon: '👨👩👧👦', titleKey: 'landing.features.familySharing.title', descKey: 'landing.features.familySharing.desc' },
              { icon: '🎯', titleKey: 'landing.features.prioritySorting.title', descKey: 'landing.features.prioritySorting.desc' },
              { icon: '📱', titleKey: 'landing.features.multiPlatform.title', descKey: 'landing.features.multiPlatform.desc' },
              { icon: '🧠', titleKey: 'landing.features.learningAI.title', descKey: 'landing.features.learningAI.desc' }
            ].map((feature, index) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                <FeatureCard interactive>
                  <FeatureCardContent>
                    <FeatureIcon>{feature.icon}</FeatureIcon>
                    <FeatureTitle variant="h6">{t(feature.titleKey)}</FeatureTitle>
                    <FeatureDescription variant="body2">{t(feature.descKey)}</FeatureDescription>
                  </FeatureCardContent>
                </FeatureCard>
              </Grid>
            ))}
          </CardGrid>
        </ContentContainer>
      </Section>

      <StatsContainer>
        <ContentContainer maxWidth="lg">
          <Grid container spacing={4}>
            {[
              { number: '98%', labelKey: 'landing.stats.accuracy' },
              { number: '2.5k+', labelKey: 'landing.stats.families' },
              { number: '15min', labelKey: 'landing.stats.timeSaved' },
              { number: 'Zero', labelKey: 'landing.stats.missedEvents' }
            ].map((stat, index) => (
              <Grid size={{ xs: 6, md: 3 }} key={index}>
                <StatNumber variant="h3">
                  {stat.number}
                </StatNumber>
                <StatLabel variant="body1">{t(stat.labelKey)}</StatLabel>
              </Grid>
            ))}
          </Grid>
        </ContentContainer>
      </StatsContainer>

      {/* About Section */}
      <Section id="about-section" spacing="large" background="paper">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.about')}
          </SectionTitle>
          <CenteredTypography variant="body1">
            Learn more about our mission to help families stay organized and never miss important events.
          </CenteredTypography>
        </ContentContainer>
      </Section>

      {/* Pricing Section */}
      <Section id="pricing-section" spacing="large">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.pricing')}
          </SectionTitle>
          <CenteredTypography variant="body1">
           Pricing info - TBD
          </CenteredTypography>
        </ContentContainer>
      </Section>

    </PageContainer>
  );
};

export default LandingPage;